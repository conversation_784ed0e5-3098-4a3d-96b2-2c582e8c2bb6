import React, { useEffect, useState } from 'react';
import { useMetaPixelEvents } from '@/components/analytics/MetaPixelTracker';

/**
 * Example component showing how to implement Meta Pixel tracking
 * for e-commerce events in a product page.
 * 
 * This is an example implementation - adapt to your actual product structure.
 */

interface Product {
  id: string;
  name: string;
  price: number;
  currency: string;
  category: string;
  brand?: string;
  sku?: string;
}

interface ProductPageProps {
  product: Product;
}

export function ProductPageExample({ product }: ProductPageProps) {
  const {
    trackViewContent,
    trackAddToCart,
    trackInitiateCheckout,
    isEnabled,
    hasConsent
  } = useMetaPixelEvents();

  const [quantity, setQuantity] = useState(1);
  const [isInCart, setIsInCart] = useState(false);

  // Track product view when component mounts
  useEffect(() => {
    if (isEnabled && hasConsent) {
      trackViewContent(
        'product',
        [product.id],
        product.name
      );
    }
  }, [product.id, trackViewContent, isEnabled, hasConsent]);

  const handleAddToCart = () => {
    // Track add to cart event
    if (isEnabled && hasConsent) {
      trackAddToCart(
        [product.id],
        product.price * quantity,
        product.currency
      );
    }

    // Your actual add to cart logic here
    setIsInCart(true);
    console.log(`Added ${quantity} of ${product.name} to cart`);
  };

  const handleBuyNow = () => {
    // Track initiate checkout event
    if (isEnabled && hasConsent) {
      trackInitiateCheckout(
        product.price * quantity,
        product.currency,
        quantity
      );
    }

    // Your actual buy now logic here
    console.log(`Initiating checkout for ${quantity} of ${product.name}`);
  };

  return (
    <div className="product-page">
      <h1>{product.name}</h1>
      <p>Price: {product.currency} {product.price}</p>
      <p>Category: {product.category}</p>
      {product.brand && <p>Brand: {product.brand}</p>}
      
      <div className="quantity-selector">
        <label htmlFor="quantity">Quantity:</label>
        <input
          id="quantity"
          type="number"
          min="1"
          value={quantity}
          onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
        />
      </div>

      <div className="actions">
        <button 
          onClick={handleAddToCart}
          disabled={isInCart}
          className="add-to-cart-btn"
        >
          {isInCart ? 'Added to Cart' : 'Add to Cart'}
        </button>
        
        <button 
          onClick={handleBuyNow}
          className="buy-now-btn"
        >
          Buy Now
        </button>
      </div>
    </div>
  );
}

/**
 * Example checkout component showing purchase tracking
 */
interface CheckoutProps {
  cartItems: Array<{
    product: Product;
    quantity: number;
  }>;
  total: number;
  currency: string;
}

export function CheckoutExample({ cartItems, total, currency }: CheckoutProps) {
  const { trackPurchase, isEnabled, hasConsent } = useMetaPixelEvents();

  const handlePurchaseComplete = (orderId: string) => {
    if (isEnabled && hasConsent) {
      // Track purchase with all product IDs
      const contentIds = cartItems.map(item => item.product.id);
      
      trackPurchase(total, currency, contentIds);
    }

    // Your actual purchase completion logic here
    console.log(`Purchase completed: Order ${orderId}`);
  };

  return (
    <div className="checkout">
      <h2>Checkout</h2>
      
      <div className="cart-summary">
        {cartItems.map(item => (
          <div key={item.product.id} className="cart-item">
            <span>{item.product.name}</span>
            <span>Qty: {item.quantity}</span>
            <span>{currency} {item.product.price * item.quantity}</span>
          </div>
        ))}
      </div>
      
      <div className="total">
        <strong>Total: {currency} {total}</strong>
      </div>
      
      <button 
        onClick={() => handlePurchaseComplete('ORDER-123')}
        className="complete-purchase-btn"
      >
        Complete Purchase
      </button>
    </div>
  );
}

/**
 * Example search component with Meta Pixel tracking
 */
export function SearchExample() {
  const { trackSearch, isEnabled, hasConsent } = useMetaPixelEvents();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);

  const handleSearch = async (query: string) => {
    if (query.trim().length < 3) return;

    // Track search event
    if (isEnabled && hasConsent) {
      trackSearch(query, 'products');
    }

    // Your actual search logic here
    console.log(`Searching for: ${query}`);
    // setSearchResults(await searchProducts(query));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch(searchQuery);
  };

  return (
    <div className="search">
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search products..."
        />
        <button type="submit">Search</button>
      </form>
      
      <div className="search-results">
        {searchResults.map(product => (
          <div key={product.id} className="search-result">
            <h3>{product.name}</h3>
            <p>{product.currency} {product.price}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Example lead generation form with Meta Pixel tracking
 */
export function LeadFormExample() {
  const { trackEvent, isEnabled, hasConsent } = useMetaPixelEvents();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    interest: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Track lead event
    if (isEnabled && hasConsent) {
      trackEvent('Lead', {
        content_name: 'Contact Form',
        content_category: formData.interest
      });
    }

    // Your actual form submission logic here
    console.log('Lead form submitted:', formData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <div className="lead-form">
      <h2>Contact Us</h2>
      
      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="name">Name:</label>
          <input
            id="name"
            name="name"
            type="text"
            value={formData.name}
            onChange={handleInputChange}
            required
          />
        </div>
        
        <div>
          <label htmlFor="email">Email:</label>
          <input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            required
          />
        </div>
        
        <div>
          <label htmlFor="phone">Phone:</label>
          <input
            id="phone"
            name="phone"
            type="tel"
            value={formData.phone}
            onChange={handleInputChange}
          />
        </div>
        
        <div>
          <label htmlFor="interest">Interest:</label>
          <select
            id="interest"
            name="interest"
            value={formData.interest}
            onChange={handleInputChange}
            required
          >
            <option value="">Select...</option>
            <option value="products">Products</option>
            <option value="services">Services</option>
            <option value="support">Support</option>
          </select>
        </div>
        
        <button type="submit">Submit</button>
      </form>
    </div>
  );
}
