import { usePage } from '@inertiajs/react';
import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';
import {
  shouldAllowTracking,
  isDoNotTrackEnabled,
  getStoredConsent,
  storeConsentData,
  isConsentValid,
  sanitizeEventData,
  type PrivacyConfig,
} from '@/utils/metaPixelPrivacy';

interface MetaPixelConfig {
  enabled: boolean;
  pixel_id?: string;
  debug_mode?: boolean;
  respect_do_not_track?: boolean;
  require_consent?: boolean;
  consent_settings?: Record<string, string>;
  automatic_matching?: boolean;
  enable_deduplication?: boolean;
  lazy_load?: boolean;
  event_delay_ms?: number;
  enabled_events?: string[];
}

interface MetaPixelContextType {
  config: MetaPixelConfig;
  isLoaded: boolean;
  hasConsent: boolean;
  trackEvent: (eventName: string, eventData?: Record<string, any>, eventId?: string) => void;
  trackPageView: (eventId?: string) => void;
  trackPurchase: (value: number, currency: string, contentIds?: string[], eventId?: string) => void;
  trackLead: (value?: number, currency?: string, eventId?: string) => void;
  trackSearch: (searchString: string, contentCategory?: string, eventId?: string) => void;
  trackViewContent: (contentType: string, contentIds?: string[], contentName?: string, eventId?: string) => void;
  trackAddToCart: (contentIds: string[], value?: number, currency?: string, eventId?: string) => void;
  trackInitiateCheckout: (value?: number, currency?: string, numItems?: number, eventId?: string) => void;
  grantConsent: () => void;
  revokeConsent: () => void;
  generateEventId: () => string;
}

const MetaPixelContext = createContext<MetaPixelContextType | null>(null);

interface MetaPixelProviderProps {
  children: React.ReactNode;
}

declare global {
  interface Window {
    fbq: any;
    _fbq: any;
  }
}

export function MetaPixelProvider({ children }: MetaPixelProviderProps) {
  const { props } = usePage();
  const config = (props.metaPixel as MetaPixelConfig) || { enabled: false };

  const [isLoaded, setIsLoaded] = useState(false);
  const [hasConsent, setHasConsent] = useState(false);

  // Privacy configuration
  const privacyConfig: PrivacyConfig = {
    respectDoNotTrack: config.respect_do_not_track ?? true,
    requireConsent: config.require_consent ?? true,
    anonymizeData: true, // Always anonymize for privacy
    dataRetentionDays: 730, // 2 years default
    excludedUserAgents: ['bot', 'crawler', 'spider', 'scraper'],
  };

  // Check if we should respect Do Not Track
  const shouldRespectDoNotTrack = useCallback(() => {
    return config.respect_do_not_track && isDoNotTrackEnabled();
  }, [config.respect_do_not_track]);

  // Generate unique event ID for deduplication
  const generateEventId = useCallback(() => {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Load Meta Pixel script
  const loadMetaPixel = useCallback(() => {
    if (!config.enabled || !config.pixel_id || shouldRespectDoNotTrack()) {
      return;
    }

    // Check if already loaded
    if (window.fbq) {
      setIsLoaded(true);
      return;
    }

    // Create fbq function
    window.fbq = window.fbq || function () {
      (window.fbq.q = window.fbq.q || []).push(arguments);
    };
    window._fbq = window._fbq || window.fbq;
    window.fbq.push = window.fbq;
    window.fbq.loaded = true;
    window.fbq.version = '2.0';
    window.fbq.queue = [];

    // Load the script
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://connect.facebook.net/en_US/fbevents.js';
    script.onload = () => {
      setIsLoaded(true);

      // Initialize pixel
      if (config.automatic_matching) {
        window.fbq('init', config.pixel_id, {
          em: 'insert_email_variable',
          fn: 'insert_first_name_variable',
          ln: 'insert_last_name_variable',
          ph: 'insert_phone_variable',
        });
      } else {
        window.fbq('init', config.pixel_id);
      }

      // Track initial page view if not lazy loaded
      if (!config.lazy_load) {
        trackPageView();
      }

      if (config.debug_mode) {
        console.log('Meta Pixel loaded and initialized');
      }
    };

    document.head.appendChild(script);

    // Add noscript fallback
    const noscript = document.createElement('noscript');
    const img = document.createElement('img');
    img.height = 1;
    img.width = 1;
    img.style.display = 'none';
    img.src = `https://www.facebook.com/tr?id=${config.pixel_id}&ev=PageView&noscript=1`;
    noscript.appendChild(img);
    document.head.appendChild(noscript);
  }, [config, shouldRespectDoNotTrack]);

  // Check consent status
  useEffect(() => {
    if (!privacyConfig.requireConsent) {
      setHasConsent(true);
      return;
    }

    // Check if tracking should be allowed based on privacy settings
    if (!shouldAllowTracking(privacyConfig)) {
      setHasConsent(false);
      return;
    }

    // Check for existing consent with validation
    const storedConsent = getStoredConsent();
    const isValid = isConsentValid(storedConsent, '1.0', 365);

    setHasConsent(isValid && storedConsent?.granted === true);
  }, [privacyConfig]);

  // Load Meta Pixel when consent is granted
  useEffect(() => {
    if (hasConsent && !shouldRespectDoNotTrack()) {
      if (config.lazy_load) {
        // Load on user interaction
        const loadOnInteraction = () => {
          loadMetaPixel();
          document.removeEventListener('click', loadOnInteraction);
          document.removeEventListener('scroll', loadOnInteraction);
          document.removeEventListener('keydown', loadOnInteraction);
        };

        document.addEventListener('click', loadOnInteraction);
        document.addEventListener('scroll', loadOnInteraction);
        document.addEventListener('keydown', loadOnInteraction);

        return () => {
          document.removeEventListener('click', loadOnInteraction);
          document.removeEventListener('scroll', loadOnInteraction);
          document.removeEventListener('keydown', loadOnInteraction);
        };
      } else {
        loadMetaPixel();
      }
    }
  }, [hasConsent, loadMetaPixel, config.lazy_load, shouldRespectDoNotTrack]);

  // Generic event tracking function
  const trackEvent = useCallback((
    eventName: string,
    eventData: Record<string, any> = {},
    eventId?: string
  ) => {
    if (!isLoaded || !hasConsent || shouldRespectDoNotTrack()) {
      if (config.debug_mode) {
        console.log('Meta Pixel event blocked:', { eventName, eventData, reason: 'not loaded, no consent, or DNT' });
      }
      return;
    }

    if (!config.enabled_events?.includes(eventName)) {
      if (config.debug_mode) {
        console.log('Meta Pixel event blocked:', { eventName, reason: 'event not enabled' });
      }
      return;
    }

    // Sanitize event data for privacy compliance
    const sanitizedData = sanitizeEventData(eventData, privacyConfig);
    const finalEventData = { ...sanitizedData };

    // Add event ID for deduplication if enabled
    if (config.enable_deduplication && eventId) {
      finalEventData.event_id = eventId;
    }

    // Add delay if configured
    const executeTrack = () => {
      try {
        window.fbq('track', eventName, finalEventData);

        if (config.debug_mode) {
          console.log('Meta Pixel event tracked:', { eventName, eventData: finalEventData });
        }
      } catch (error) {
        console.error('Meta Pixel tracking error:', error);
      }
    };

    if (config.event_delay_ms && config.event_delay_ms > 0) {
      setTimeout(executeTrack, config.event_delay_ms);
    } else {
      executeTrack();
    }
  }, [isLoaded, hasConsent, config, shouldRespectDoNotTrack]);

  // Specific event tracking functions
  const trackPageView = useCallback((eventId?: string) => {
    trackEvent('PageView', {}, eventId);
  }, [trackEvent]);

  const trackPurchase = useCallback((
    value: number,
    currency: string,
    contentIds?: string[],
    eventId?: string
  ) => {
    const eventData: Record<string, any> = { value, currency };
    if (contentIds && contentIds.length > 0) {
      eventData.content_ids = contentIds;
      eventData.content_type = 'product';
    }
    trackEvent('Purchase', eventData, eventId);
  }, [trackEvent]);

  const trackLead = useCallback((value?: number, currency?: string, eventId?: string) => {
    const eventData: Record<string, any> = {};
    if (value !== undefined) eventData.value = value;
    if (currency) eventData.currency = currency;
    trackEvent('Lead', eventData, eventId);
  }, [trackEvent]);

  const trackSearch = useCallback((
    searchString: string,
    contentCategory?: string,
    eventId?: string
  ) => {
    const eventData: Record<string, any> = { search_string: searchString };
    if (contentCategory) eventData.content_category = contentCategory;
    trackEvent('Search', eventData, eventId);
  }, [trackEvent]);

  const trackViewContent = useCallback((
    contentType: string,
    contentIds?: string[],
    contentName?: string,
    eventId?: string
  ) => {
    const eventData: Record<string, any> = { content_type: contentType };
    if (contentIds && contentIds.length > 0) eventData.content_ids = contentIds;
    if (contentName) eventData.content_name = contentName;
    trackEvent('ViewContent', eventData, eventId);
  }, [trackEvent]);

  const trackAddToCart = useCallback((
    contentIds: string[],
    value?: number,
    currency?: string,
    eventId?: string
  ) => {
    const eventData: Record<string, any> = {
      content_ids: contentIds,
      content_type: 'product'
    };
    if (value !== undefined) eventData.value = value;
    if (currency) eventData.currency = currency;
    trackEvent('AddToCart', eventData, eventId);
  }, [trackEvent]);

  const trackInitiateCheckout = useCallback((
    value?: number,
    currency?: string,
    numItems?: number,
    eventId?: string
  ) => {
    const eventData: Record<string, any> = {};
    if (value !== undefined) eventData.value = value;
    if (currency) eventData.currency = currency;
    if (numItems !== undefined) eventData.num_items = numItems;
    trackEvent('InitiateCheckout', eventData, eventId);
  }, [trackEvent]);

  // Consent management
  const grantConsent = useCallback(() => {
    storeConsentData(true, '1.0');
    setHasConsent(true);

    if (config.debug_mode) {
      console.log('Meta Pixel consent granted');
    }
  }, [config.debug_mode]);

  const revokeConsent = useCallback(() => {
    storeConsentData(false, '1.0');
    setHasConsent(false);

    if (config.debug_mode) {
      console.log('Meta Pixel consent revoked');
    }
  }, [config.debug_mode]);

  const contextValue: MetaPixelContextType = {
    config,
    isLoaded,
    hasConsent,
    trackEvent,
    trackPageView,
    trackPurchase,
    trackLead,
    trackSearch,
    trackViewContent,
    trackAddToCart,
    trackInitiateCheckout,
    grantConsent,
    revokeConsent,
    generateEventId,
  };

  return (
    <MetaPixelContext.Provider value={contextValue}>
      {children}
    </MetaPixelContext.Provider>
  );
}

export function useMetaPixel() {
  const context = useContext(MetaPixelContext);
  if (!context) {
    throw new Error('useMetaPixel must be used within a MetaPixelProvider');
  }
  return context;
}
