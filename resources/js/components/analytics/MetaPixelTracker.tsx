import React, { useEffect } from 'react';
import { useMetaPixel } from '@/components/analytics/MetaPixelProvider';
import { usePage } from '@inertiajs/react';

interface MetaPixelTrackerProps {
  children: React.ReactNode;
}

/**
 * MetaPixelTracker component that automatically tracks page views
 * and provides context for manual event tracking throughout the app.
 */
export function MetaPixelTracker({ children }: MetaPixelTrackerProps) {
  const metaPixel = useMetaPixel();
  const { url } = usePage();

  // Automatically track page views when URL changes
  useEffect(() => {
    if (metaPixel.isLoaded && metaPixel.hasConsent) {
      metaPixel.trackPageView();
    }
  }, [url, metaPixel.isLoaded, metaPixel.hasConsent, metaPixel]);

  return <>{children}</>;
}

/**
 * Hook for manual event tracking with automatic event ID generation
 */
export function useMetaPixelEvents() {
  const metaPixel = useMetaPixel();

  const trackEvent = (eventName: string, eventData?: Record<string, any>) => {
    if (!metaPixel.isLoaded || !metaPixel.hasConsent) {
      return;
    }

    const eventId = metaPixel.generateEventId();
    metaPixel.trackEvent(eventName, eventData, eventId);
  };

  const trackPurchase = (value: number, currency: string = 'USD', contentIds?: string[]) => {
    if (!metaPixel.isLoaded || !metaPixel.hasConsent) {
      return;
    }

    const eventId = metaPixel.generateEventId();
    metaPixel.trackPurchase(value, currency, contentIds, eventId);
  };

  const trackLead = (value?: number, currency: string = 'USD') => {
    if (!metaPixel.isLoaded || !metaPixel.hasConsent) {
      return;
    }

    const eventId = metaPixel.generateEventId();
    metaPixel.trackLead(value, currency, eventId);
  };

  const trackViewContent = (contentType: string, contentIds?: string[], contentName?: string) => {
    if (!metaPixel.isLoaded || !metaPixel.hasConsent) {
      return;
    }

    const eventId = metaPixel.generateEventId();
    metaPixel.trackViewContent(contentType, contentIds, contentName, eventId);
  };

  const trackAddToCart = (contentIds: string[], value?: number, currency: string = 'USD') => {
    if (!metaPixel.isLoaded || !metaPixel.hasConsent) {
      return;
    }

    const eventId = metaPixel.generateEventId();
    metaPixel.trackAddToCart(contentIds, value, currency, eventId);
  };

  const trackInitiateCheckout = (value?: number, currency: string = 'USD', numItems?: number) => {
    if (!metaPixel.isLoaded || !metaPixel.hasConsent) {
      return;
    }

    const eventId = metaPixel.generateEventId();
    metaPixel.trackInitiateCheckout(value, currency, numItems, eventId);
  };

  const trackSearch = (searchString: string, contentCategory?: string) => {
    if (!metaPixel.isLoaded || !metaPixel.hasConsent) {
      return;
    }

    const eventId = metaPixel.generateEventId();
    metaPixel.trackSearch(searchString, contentCategory, eventId);
  };

  return {
    trackEvent,
    trackPurchase,
    trackLead,
    trackViewContent,
    trackAddToCart,
    trackInitiateCheckout,
    trackSearch,
    isEnabled: metaPixel.config.enabled,
    hasConsent: metaPixel.hasConsent,
    isLoaded: metaPixel.isLoaded,
  };
}

/**
 * Higher-order component for tracking specific events on component mount
 */
export function withMetaPixelTracking<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  eventName: string,
  eventDataFactory?: (props: P) => Record<string, any>
) {
  return function MetaPixelTrackedComponent(props: P) {
    const { trackEvent } = useMetaPixelEvents();

    useEffect(() => {
      const eventData = eventDataFactory ? eventDataFactory(props) : {};
      trackEvent(eventName, eventData);
    }, [trackEvent]);

    return <WrappedComponent {...props} />;
  };
}

/**
 * Component for tracking events on user interactions
 */
interface TrackingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  eventName: string;
  eventData?: Record<string, any>;
  children: React.ReactNode;
}

export function TrackingButton({ eventName, eventData, children, onClick, ...props }: TrackingButtonProps) {
  const { trackEvent } = useMetaPixelEvents();

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    trackEvent(eventName, eventData);
    onClick?.(e);
  };

  return (
    <button onClick={handleClick} {...props}>
      {children}
    </button>
  );
}

/**
 * Component for tracking link clicks
 */
interface TrackingLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  eventName: string;
  eventData?: Record<string, any>;
  children: React.ReactNode;
}

export function TrackingLink({ eventName, eventData, children, onClick, ...props }: TrackingLinkProps) {
  const { trackEvent } = useMetaPixelEvents();

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    trackEvent(eventName, eventData);
    onClick?.(e);
  };

  return (
    <a onClick={handleClick} {...props}>
      {children}
    </a>
  );
}

/**
 * Hook for tracking form submissions
 */
export function useFormTracking(eventName: string = 'SubmitApplication') {
  const { trackEvent } = useMetaPixelEvents();

  const trackFormSubmission = (formData?: Record<string, any>) => {
    trackEvent(eventName, formData);
  };

  return { trackFormSubmission };
}

/**
 * Hook for tracking search events with debouncing
 */
export function useSearchTracking(debounceMs: number = 1000) {
  const { trackSearch } = useMetaPixelEvents();
  const [searchTimeout, setSearchTimeout] = React.useState<NodeJS.Timeout | null>(null);

  const trackSearchWithDebounce = (searchString: string, contentCategory?: string) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      if (searchString.trim().length > 2) { // Only track searches with 3+ characters
        trackSearch(searchString, contentCategory);
      }
    }, debounceMs);

    setSearchTimeout(timeout);
  };

  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  return { trackSearchWithDebounce };
}

/**
 * Hook for tracking scroll depth
 */
export function useScrollTracking(thresholds: number[] = [25, 50, 75, 90]) {
  const { trackEvent } = useMetaPixelEvents();
  const [trackedThresholds, setTrackedThresholds] = React.useState<Set<number>>(new Set());

  useEffect(() => {
    const handleScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );

      thresholds.forEach(threshold => {
        if (scrollPercent >= threshold && !trackedThresholds.has(threshold)) {
          trackEvent('ScrollDepth', { scroll_percent: threshold });
          setTrackedThresholds(prev => new Set(prev).add(threshold));
        }
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [trackEvent, thresholds, trackedThresholds]);

  return { trackedThresholds };
}
