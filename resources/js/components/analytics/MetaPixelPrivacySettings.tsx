import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Shield, Eye, EyeOff, Cookie, AlertTriangle, CheckCircle, Info } from 'lucide-react';
import { useMetaPixel } from '@/components/analytics/MetaPixelProvider';

interface MetaPixelPrivacySettingsProps {
  showTitle?: boolean;
  className?: string;
}

export function MetaPixelPrivacySettings({ 
  showTitle = true, 
  className = '' 
}: MetaPixelPrivacySettingsProps) {
  const metaPixel = useMetaPixel();
  const [localConsent, setLocalConsent] = useState(metaPixel.hasConsent);
  const [doNotTrackEnabled, setDoNotTrackEnabled] = useState(false);

  useEffect(() => {
    // Check Do Not Track status
    const isDNTEnabled = navigator.doNotTrack === '1' || 
                        navigator.doNotTrack === 'yes' || 
                        (window as any).doNotTrack === '1';
    setDoNotTrackEnabled(isDNTEnabled);
  }, []);

  useEffect(() => {
    setLocalConsent(metaPixel.hasConsent);
  }, [metaPixel.hasConsent]);

  const handleConsentToggle = (granted: boolean) => {
    if (granted) {
      metaPixel.grantConsent();
    } else {
      metaPixel.revokeConsent();
    }
    setLocalConsent(granted);
  };

  const getPrivacyStatus = () => {
    if (doNotTrackEnabled && metaPixel.config.respect_do_not_track) {
      return {
        status: 'blocked',
        icon: <EyeOff className="h-4 w-4" />,
        text: 'Tracking Blocked (Do Not Track)',
        description: 'Meta Pixel tracking is disabled due to Do Not Track browser setting.',
        variant: 'destructive' as const
      };
    }

    if (!metaPixel.config.enabled) {
      return {
        status: 'disabled',
        icon: <EyeOff className="h-4 w-4" />,
        text: 'Meta Pixel Disabled',
        description: 'Meta Pixel tracking is disabled by administrator.',
        variant: 'secondary' as const
      };
    }

    if (!localConsent) {
      return {
        status: 'no-consent',
        icon: <Shield className="h-4 w-4" />,
        text: 'Consent Required',
        description: 'Meta Pixel tracking requires your consent to collect data.',
        variant: 'default' as const
      };
    }

    if (metaPixel.isLoaded) {
      return {
        status: 'active',
        icon: <CheckCircle className="h-4 w-4" />,
        text: 'Active',
        description: 'Meta Pixel is actively tracking with your consent.',
        variant: 'default' as const
      };
    }

    return {
      status: 'loading',
      icon: <Eye className="h-4 w-4" />,
      text: 'Loading',
      description: 'Meta Pixel is loading...',
      variant: 'secondary' as const
    };
  };

  const privacyStatus = getPrivacyStatus();
  const canToggleConsent = metaPixel.config.enabled && 
                          (!doNotTrackEnabled || !metaPixel.config.respect_do_not_track);

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Meta Pixel Privacy Settings
          </CardTitle>
          <CardDescription>
            Manage your Meta Pixel tracking preferences and privacy settings.
          </CardDescription>
        </CardHeader>
      )}
      
      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Tracking Status</Label>
            <Badge variant={privacyStatus.variant} className="flex items-center gap-1">
              {privacyStatus.icon}
              {privacyStatus.text}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">
            {privacyStatus.description}
          </p>
        </div>

        <Separator />

        {/* Consent Toggle */}
        {canToggleConsent && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="meta-pixel-consent" className="text-sm font-medium">
                  Allow Meta Pixel Tracking
                </Label>
                <p className="text-sm text-muted-foreground">
                  Enable Meta Pixel to track your interactions for analytics and advertising.
                </p>
              </div>
              <Switch
                id="meta-pixel-consent"
                checked={localConsent}
                onCheckedChange={handleConsentToggle}
              />
            </div>
          </div>
        )}

        {/* Do Not Track Warning */}
        {doNotTrackEnabled && metaPixel.config.respect_do_not_track && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Your browser's "Do Not Track" setting is enabled. Meta Pixel tracking is automatically disabled to respect your privacy preference.
            </AlertDescription>
          </Alert>
        )}

        {/* Privacy Information */}
        <div className="space-y-4">
          <Label className="text-sm font-medium">Privacy Information</Label>
          
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <Cookie className="h-4 w-4 mt-0.5 text-muted-foreground" />
              <div>
                <p className="font-medium">Data Collection</p>
                <p className="text-muted-foreground">
                  Meta Pixel collects information about your interactions with our website to improve our services and show relevant advertisements.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-2">
              <Shield className="h-4 w-4 mt-0.5 text-muted-foreground" />
              <div>
                <p className="font-medium">Data Protection</p>
                <p className="text-muted-foreground">
                  Your data is processed in accordance with Meta's privacy policy and applicable data protection laws.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 mt-0.5 text-muted-foreground" />
              <div>
                <p className="font-medium">Your Rights</p>
                <p className="text-muted-foreground">
                  You can withdraw consent at any time. This will stop future data collection but won't affect previously collected data.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Configuration Details */}
        {metaPixel.config.enabled && (
          <>
            <Separator />
            <div className="space-y-3">
              <Label className="text-sm font-medium">Configuration</Label>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium">Pixel ID</p>
                  <p className="text-muted-foreground font-mono">
                    {metaPixel.config.pixel_id ? 
                      `${metaPixel.config.pixel_id.substring(0, 8)}...` : 
                      'Not configured'
                    }
                  </p>
                </div>
                <div>
                  <p className="font-medium">Debug Mode</p>
                  <p className="text-muted-foreground">
                    {metaPixel.config.debug_mode ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
                <div>
                  <p className="font-medium">Automatic Matching</p>
                  <p className="text-muted-foreground">
                    {metaPixel.config.automatic_matching ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
                <div>
                  <p className="font-medium">Deduplication</p>
                  <p className="text-muted-foreground">
                    {metaPixel.config.enable_deduplication ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Actions */}
        {canToggleConsent && (
          <>
            <Separator />
            <div className="flex gap-2">
              {localConsent ? (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleConsentToggle(false)}
                >
                  Revoke Consent
                </Button>
              ) : (
                <Button 
                  size="sm"
                  onClick={() => handleConsentToggle(true)}
                >
                  Grant Consent
                </Button>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Compact version for use in cookie banners or small spaces
 */
export function MetaPixelPrivacyToggle() {
  const metaPixel = useMetaPixel();
  const [localConsent, setLocalConsent] = useState(metaPixel.hasConsent);

  useEffect(() => {
    setLocalConsent(metaPixel.hasConsent);
  }, [metaPixel.hasConsent]);

  const handleToggle = (granted: boolean) => {
    if (granted) {
      metaPixel.grantConsent();
    } else {
      metaPixel.revokeConsent();
    }
    setLocalConsent(granted);
  };

  if (!metaPixel.config.enabled) {
    return null;
  }

  return (
    <div className="flex items-center justify-between">
      <div className="space-y-1">
        <Label htmlFor="meta-pixel-toggle" className="text-sm">
          Meta Pixel
        </Label>
        <p className="text-xs text-muted-foreground">
          Analytics and advertising tracking
        </p>
      </div>
      <Switch
        id="meta-pixel-toggle"
        checked={localConsent}
        onCheckedChange={handleToggle}
      />
    </div>
  );
}
