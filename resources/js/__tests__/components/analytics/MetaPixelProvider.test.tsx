import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { usePage } from '@inertiajs/react';
import { MetaPixelProvider, useMetaPixel } from '@/components/analytics/MetaPixelProvider';

// Mock Inertia
jest.mock('@inertiajs/react', () => ({
  usePage: jest.fn(),
}));

// Mock privacy utilities
jest.mock('@/utils/metaPixelPrivacy', () => ({
  isDoNotTrackEnabled: jest.fn(() => false),
  shouldAllowTracking: jest.fn(() => true),
  getStoredConsent: jest.fn(() => ({ granted: true, timestamp: '2023-01-01', version: '1.0' })),
  storeConsentData: jest.fn(),
  isConsentValid: jest.fn(() => true),
  sanitizeEventData: jest.fn((data) => data),
  getPrivacyCompliantUserProperties: jest.fn(() => ({})),
}));

// Mock window.fbq
const mockFbq = jest.fn();
Object.defineProperty(window, 'fbq', {
  value: mockFbq,
  writable: true,
});

// Test component that uses the Meta Pixel context
function TestComponent() {
  const metaPixel = useMetaPixel();
  
  return (
    <div>
      <div data-testid="enabled">{metaPixel.config.enabled ? 'true' : 'false'}</div>
      <div data-testid="loaded">{metaPixel.isLoaded ? 'true' : 'false'}</div>
      <div data-testid="consent">{metaPixel.hasConsent ? 'true' : 'false'}</div>
      <div data-testid="pixel-id">{metaPixel.config.pixel_id || 'none'}</div>
      <button 
        data-testid="track-event" 
        onClick={() => metaPixel.trackEvent('TestEvent', { test: 'data' })}
      >
        Track Event
      </button>
      <button 
        data-testid="track-purchase" 
        onClick={() => metaPixel.trackPurchase(99.99, 'USD', ['product_123'])}
      >
        Track Purchase
      </button>
      <button 
        data-testid="grant-consent" 
        onClick={() => metaPixel.grantConsent()}
      >
        Grant Consent
      </button>
      <button 
        data-testid="revoke-consent" 
        onClick={() => metaPixel.revokeConsent()}
      >
        Revoke Consent
      </button>
    </div>
  );
}

describe('MetaPixelProvider', () => {
  const mockUsePage = usePage as jest.MockedFunction<typeof usePage>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockFbq.mockClear();
    
    // Reset DOM
    document.head.innerHTML = '';
    document.body.innerHTML = '';
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
      },
      writable: true,
    });
  });

  it('renders children and provides Meta Pixel context', () => {
    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: true,
          pixel_id: 'test_pixel_123',
          require_consent: false,
        },
      },
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    expect(screen.getByTestId('enabled')).toHaveTextContent('true');
    expect(screen.getByTestId('pixel-id')).toHaveTextContent('test_pixel_123');
  });

  it('handles disabled Meta Pixel configuration', () => {
    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: false,
          pixel_id: 'test_pixel_123',
        },
      },
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    expect(screen.getByTestId('enabled')).toHaveTextContent('false');
  });

  it('handles missing Meta Pixel configuration', () => {
    mockUsePage.mockReturnValue({
      props: {},
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    expect(screen.getByTestId('enabled')).toHaveTextContent('false');
    expect(screen.getByTestId('pixel-id')).toHaveTextContent('none');
  });

  it('loads Meta Pixel script when enabled and consent granted', async () => {
    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: true,
          pixel_id: 'test_pixel_123',
          require_consent: false,
          respect_do_not_track: false,
        },
      },
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('loaded')).toHaveTextContent('true');
    });

    // Check that script was added to head
    const scripts = document.head.querySelectorAll('script');
    expect(scripts.length).toBeGreaterThan(0);
    
    // Check that fbq was called
    expect(mockFbq).toHaveBeenCalledWith('init', 'test_pixel_123');
  });

  it('does not load Meta Pixel when consent is required but not granted', () => {
    const mockGetStoredConsent = require('@/utils/metaPixelPrivacy').getStoredConsent;
    mockGetStoredConsent.mockReturnValue({ granted: false, timestamp: '2023-01-01', version: '1.0' });

    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: true,
          pixel_id: 'test_pixel_123',
          require_consent: true,
        },
      },
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    expect(screen.getByTestId('consent')).toHaveTextContent('false');
    expect(screen.getByTestId('loaded')).toHaveTextContent('false');
  });

  it('tracks events when loaded and consent granted', async () => {
    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: true,
          pixel_id: 'test_pixel_123',
          require_consent: false,
          enabled_events: ['TestEvent'],
        },
      },
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('loaded')).toHaveTextContent('true');
    });

    act(() => {
      screen.getByTestId('track-event').click();
    });

    expect(mockFbq).toHaveBeenCalledWith('track', 'TestEvent', expect.objectContaining({
      test: 'data',
    }));
  });

  it('tracks purchase events with correct data', async () => {
    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: true,
          pixel_id: 'test_pixel_123',
          require_consent: false,
          enabled_events: ['Purchase'],
        },
      },
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('loaded')).toHaveTextContent('true');
    });

    act(() => {
      screen.getByTestId('track-purchase').click();
    });

    expect(mockFbq).toHaveBeenCalledWith('track', 'Purchase', expect.objectContaining({
      value: 99.99,
      currency: 'USD',
      content_ids: ['product_123'],
    }));
  });

  it('does not track events when event is not enabled', async () => {
    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: true,
          pixel_id: 'test_pixel_123',
          require_consent: false,
          enabled_events: ['Purchase'], // TestEvent not enabled
        },
      },
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('loaded')).toHaveTextContent('true');
    });

    act(() => {
      screen.getByTestId('track-event').click();
    });

    // Should not call fbq for TestEvent
    expect(mockFbq).not.toHaveBeenCalledWith('track', 'TestEvent', expect.anything());
  });

  it('handles consent granting and revoking', () => {
    const mockStoreConsentData = require('@/utils/metaPixelPrivacy').storeConsentData;

    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: true,
          pixel_id: 'test_pixel_123',
          require_consent: true,
        },
      },
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    // Grant consent
    act(() => {
      screen.getByTestId('grant-consent').click();
    });

    expect(mockStoreConsentData).toHaveBeenCalledWith(true, '1.0');
    expect(screen.getByTestId('consent')).toHaveTextContent('true');

    // Revoke consent
    act(() => {
      screen.getByTestId('revoke-consent').click();
    });

    expect(mockStoreConsentData).toHaveBeenCalledWith(false, '1.0');
    expect(screen.getByTestId('consent')).toHaveTextContent('false');
  });

  it('respects Do Not Track setting', () => {
    const mockIsDoNotTrackEnabled = require('@/utils/metaPixelPrivacy').isDoNotTrackEnabled;
    mockIsDoNotTrackEnabled.mockReturnValue(true);

    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: true,
          pixel_id: 'test_pixel_123',
          respect_do_not_track: true,
        },
      },
    } as any);

    render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    expect(screen.getByTestId('loaded')).toHaveTextContent('false');
  });

  it('generates unique event IDs', () => {
    mockUsePage.mockReturnValue({
      props: {
        metaPixel: {
          enabled: true,
          pixel_id: 'test_pixel_123',
        },
      },
    } as any);

    const { rerender } = render(
      <MetaPixelProvider>
        <TestComponent />
      </MetaPixelProvider>
    );

    const metaPixel = useMetaPixel();
    const eventId1 = metaPixel.generateEventId();
    const eventId2 = metaPixel.generateEventId();

    expect(eventId1).toBeDefined();
    expect(eventId2).toBeDefined();
    expect(eventId1).not.toBe(eventId2);
    expect(typeof eventId1).toBe('string');
    expect(typeof eventId2).toBe('string');
  });
});
