import {
  isDoNotTrackEnabled,
  shouldAllowTracking,
  getStoredConsent,
  storeConsentData,
  isConsentValid,
  sanitizeEventData,
  getPrivacyCompliantUserProperties,
  checkPrivacyCompliance,
  anonymizeUserData,
} from '@/utils/metaPixelPrivacy';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

// Mock navigator.doNotTrack
Object.defineProperty(navigator, 'doNotTrack', {
  value: '0',
  writable: true,
});

describe('metaPixelPrivacy', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('isDoNotTrackEnabled', () => {
    it('returns true when Do Not Track is enabled', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '1', writable: true });
      expect(isDoNotTrackEnabled()).toBe(true);
    });

    it('returns false when Do Not Track is disabled', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '0', writable: true });
      expect(isDoNotTrackEnabled()).toBe(false);
    });

    it('returns false when Do Not Track is null', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: null, writable: true });
      expect(isDoNotTrackEnabled()).toBe(false);
    });

    it('returns false when Do Not Track is undefined', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: undefined, writable: true });
      expect(isDoNotTrackEnabled()).toBe(false);
    });
  });

  describe('shouldAllowTracking', () => {
    it('returns false when Do Not Track is enabled and should be respected', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '1', writable: true });
      
      const config = { respect_do_not_track: true, require_consent: false };
      expect(shouldAllowTracking(config)).toBe(false);
    });

    it('returns true when Do Not Track is enabled but should not be respected', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '1', writable: true });
      
      const config = { respect_do_not_track: false, require_consent: false };
      expect(shouldAllowTracking(config)).toBe(true);
    });

    it('returns false when consent is required but not granted', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '0', writable: true });
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        granted: false,
        timestamp: '2023-01-01T00:00:00.000Z',
        version: '1.0'
      }));
      
      const config = { respect_do_not_track: false, require_consent: true };
      expect(shouldAllowTracking(config)).toBe(false);
    });

    it('returns true when consent is required and granted', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '0', writable: true });
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        granted: true,
        timestamp: '2023-01-01T00:00:00.000Z',
        version: '1.0'
      }));
      
      const config = { respect_do_not_track: false, require_consent: true };
      expect(shouldAllowTracking(config)).toBe(true);
    });

    it('returns true when no restrictions apply', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '0', writable: true });
      
      const config = { respect_do_not_track: false, require_consent: false };
      expect(shouldAllowTracking(config)).toBe(true);
    });
  });

  describe('getStoredConsent', () => {
    it('returns null when no consent is stored', () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      expect(getStoredConsent()).toBeNull();
    });

    it('returns parsed consent data when stored', () => {
      const consentData = {
        granted: true,
        timestamp: '2023-01-01T00:00:00.000Z',
        version: '1.0'
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(consentData));
      
      expect(getStoredConsent()).toEqual(consentData);
    });

    it('returns null when stored data is invalid JSON', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-json');
      expect(getStoredConsent()).toBeNull();
    });
  });

  describe('storeConsentData', () => {
    it('stores consent data with timestamp and version', () => {
      const mockDate = new Date('2023-01-01T00:00:00.000Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
      
      storeConsentData(true, '1.0');
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'meta_pixel_consent',
        JSON.stringify({
          granted: true,
          timestamp: '2023-01-01T00:00:00.000Z',
          version: '1.0'
        })
      );
      
      jest.restoreAllMocks();
    });

    it('removes consent data when granted is false', () => {
      storeConsentData(false, '1.0');
      
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('meta_pixel_consent');
    });
  });

  describe('isConsentValid', () => {
    it('returns false when no consent data provided', () => {
      expect(isConsentValid(null)).toBe(false);
    });

    it('returns false when consent is not granted', () => {
      const consent = {
        granted: false,
        timestamp: '2023-01-01T00:00:00.000Z',
        version: '1.0'
      };
      expect(isConsentValid(consent)).toBe(false);
    });

    it('returns false when consent is expired', () => {
      const oldDate = new Date();
      oldDate.setFullYear(oldDate.getFullYear() - 2); // 2 years ago
      
      const consent = {
        granted: true,
        timestamp: oldDate.toISOString(),
        version: '1.0'
      };
      expect(isConsentValid(consent)).toBe(false);
    });

    it('returns true when consent is valid and not expired', () => {
      const recentDate = new Date();
      recentDate.setMonth(recentDate.getMonth() - 6); // 6 months ago
      
      const consent = {
        granted: true,
        timestamp: recentDate.toISOString(),
        version: '1.0'
      };
      expect(isConsentValid(consent)).toBe(true);
    });
  });

  describe('sanitizeEventData', () => {
    it('removes sensitive fields from event data', () => {
      const eventData = {
        value: 99.99,
        currency: 'USD',
        email: '<EMAIL>',
        phone: '+1234567890',
        first_name: 'John',
        last_name: 'Doe',
        content_ids: ['product_123']
      };
      
      const sanitized = sanitizeEventData(eventData);
      
      expect(sanitized).toEqual({
        value: 99.99,
        currency: 'USD',
        content_ids: ['product_123']
      });
      expect(sanitized).not.toHaveProperty('email');
      expect(sanitized).not.toHaveProperty('phone');
      expect(sanitized).not.toHaveProperty('first_name');
      expect(sanitized).not.toHaveProperty('last_name');
    });

    it('returns empty object when input is null', () => {
      expect(sanitizeEventData(null)).toEqual({});
    });

    it('returns empty object when input is undefined', () => {
      expect(sanitizeEventData(undefined)).toEqual({});
    });
  });

  describe('getPrivacyCompliantUserProperties', () => {
    it('returns empty object when no user provided', () => {
      expect(getPrivacyCompliantUserProperties(null)).toEqual({});
    });

    it('returns anonymized user properties', () => {
      const user = {
        id: 123,
        email: '<EMAIL>',
        name: 'John Doe',
        phone: '+1234567890'
      };
      
      const properties = getPrivacyCompliantUserProperties(user);
      
      expect(properties).toHaveProperty('user_id');
      expect(properties.user_id).toBe('123');
      expect(properties).not.toHaveProperty('email');
      expect(properties).not.toHaveProperty('name');
      expect(properties).not.toHaveProperty('phone');
    });
  });

  describe('checkPrivacyCompliance', () => {
    it('returns compliant status when all checks pass', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '0', writable: true });
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        granted: true,
        timestamp: new Date().toISOString(),
        version: '1.0'
      }));
      
      const config = {
        respect_do_not_track: true,
        require_consent: true
      };
      
      const compliance = checkPrivacyCompliance(config);
      
      expect(compliance.compliant).toBe(true);
      expect(compliance.reasons).toHaveLength(0);
    });

    it('returns non-compliant status when Do Not Track is enabled', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '1', writable: true });
      
      const config = {
        respect_do_not_track: true,
        require_consent: false
      };
      
      const compliance = checkPrivacyCompliance(config);
      
      expect(compliance.compliant).toBe(false);
      expect(compliance.reasons).toContain('Do Not Track is enabled');
    });

    it('returns non-compliant status when consent is required but not granted', () => {
      Object.defineProperty(navigator, 'doNotTrack', { value: '0', writable: true });
      mockLocalStorage.getItem.mockReturnValue(null);
      
      const config = {
        respect_do_not_track: false,
        require_consent: true
      };
      
      const compliance = checkPrivacyCompliance(config);
      
      expect(compliance.compliant).toBe(false);
      expect(compliance.reasons).toContain('User consent is required but not granted');
    });
  });

  describe('anonymizeUserData', () => {
    it('anonymizes sensitive user data fields', () => {
      const userData = {
        email: '<EMAIL>',
        phone: '+1234567890',
        first_name: 'John',
        last_name: 'Doe',
        city: 'New York',
        country: 'US'
      };
      
      const anonymized = anonymizeUserData(userData);
      
      expect(anonymized.email).not.toBe('<EMAIL>');
      expect(anonymized.phone).not.toBe('+1234567890');
      expect(anonymized.first_name).not.toBe('John');
      expect(anonymized.last_name).not.toBe('Doe');
      expect(anonymized.city).toBe('New York'); // Non-sensitive data preserved
      expect(anonymized.country).toBe('US'); // Non-sensitive data preserved
    });

    it('handles empty user data', () => {
      expect(anonymizeUserData({})).toEqual({});
    });

    it('handles null user data', () => {
      expect(anonymizeUserData(null)).toEqual({});
    });

    it('produces consistent hashes for same input', () => {
      const userData1 = { email: '<EMAIL>' };
      const userData2 = { email: '<EMAIL>' };
      
      const anonymized1 = anonymizeUserData(userData1);
      const anonymized2 = anonymizeUserData(userData2);
      
      expect(anonymized1.email).toBe(anonymized2.email);
    });
  });
});
