/**
 * Meta Pixel Privacy Compliance Utilities
 * 
 * This module provides utilities for ensuring Meta Pixel tracking
 * complies with privacy regulations like GDPR, CCPA, etc.
 */

export interface PrivacyConfig {
  respectDoNotTrack: boolean;
  requireConsent: boolean;
  anonymizeData: boolean;
  dataRetentionDays: number;
  excludedUserAgents: string[];
}

export interface ConsentData {
  granted: boolean;
  timestamp: string;
  version: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Check if Do Not Track is enabled in the browser
 */
export function isDoNotTrackEnabled(): boolean {
  return navigator.doNotTrack === '1' || 
         navigator.doNotTrack === 'yes' || 
         (window as any).doNotTrack === '1';
}

/**
 * Check if the current user agent should be excluded from tracking
 */
export function isUserAgentExcluded(excludedAgents: string[] = []): boolean {
  const userAgent = navigator.userAgent.toLowerCase();
  
  return excludedAgents.some(agent => 
    userAgent.includes(agent.toLowerCase())
  );
}

/**
 * Check if tracking should be allowed based on privacy settings
 */
export function shouldAllowTracking(config: PrivacyConfig): boolean {
  // Check Do Not Track
  if (config.respectDoNotTrack && isDoNotTrackEnabled()) {
    return false;
  }

  // Check user agent exclusions
  if (isUserAgentExcluded(config.excludedUserAgents)) {
    return false;
  }

  // If consent is required, it will be checked separately
  return true;
}

/**
 * Get stored consent data
 */
export function getStoredConsent(): ConsentData | null {
  try {
    const stored = localStorage.getItem('meta-pixel-consent-data');
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('Error parsing stored consent data:', error);
  }
  return null;
}

/**
 * Store consent data with timestamp and metadata
 */
export function storeConsentData(granted: boolean, version: string = '1.0'): void {
  const consentData: ConsentData = {
    granted,
    timestamp: new Date().toISOString(),
    version,
    userAgent: navigator.userAgent,
  };

  try {
    localStorage.setItem('meta-pixel-consent-data', JSON.stringify(consentData));
    
    // Also store simple consent flag for backward compatibility
    localStorage.setItem('meta-pixel-consent', granted ? 'granted' : 'denied');
  } catch (error) {
    console.error('Error storing consent data:', error);
  }
}

/**
 * Check if consent is valid (not expired and correct version)
 */
export function isConsentValid(
  consentData: ConsentData | null, 
  currentVersion: string = '1.0',
  maxAgeDays: number = 365
): boolean {
  if (!consentData) {
    return false;
  }

  // Check version
  if (consentData.version !== currentVersion) {
    return false;
  }

  // Check age
  const consentDate = new Date(consentData.timestamp);
  const maxAge = maxAgeDays * 24 * 60 * 60 * 1000; // Convert to milliseconds
  const isExpired = Date.now() - consentDate.getTime() > maxAge;

  return !isExpired;
}

/**
 * Clear all stored consent data
 */
export function clearConsentData(): void {
  localStorage.removeItem('meta-pixel-consent-data');
  localStorage.removeItem('meta-pixel-consent');
}

/**
 * Anonymize user data for privacy compliance
 */
export function anonymizeUserData(data: Record<string, any>): Record<string, any> {
  const anonymized = { ...data };

  // Remove or hash sensitive fields
  const sensitiveFields = ['email', 'phone', 'first_name', 'last_name', 'address'];
  
  sensitiveFields.forEach(field => {
    if (anonymized[field]) {
      // Hash the value instead of removing it completely
      anonymized[field] = hashString(anonymized[field]);
    }
  });

  // Remove IP address if present
  delete anonymized.client_ip_address;
  delete anonymized.ip_address;

  return anonymized;
}

/**
 * Simple hash function for anonymizing data
 */
function hashString(str: string): string {
  let hash = 0;
  if (str.length === 0) return hash.toString();
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(36);
}

/**
 * Generate privacy-compliant event data
 */
export function sanitizeEventData(
  eventData: Record<string, any>,
  config: PrivacyConfig
): Record<string, any> {
  let sanitized = { ...eventData };

  // Anonymize data if required
  if (config.anonymizeData) {
    sanitized = anonymizeUserData(sanitized);
  }

  // Add privacy metadata
  sanitized._privacy = {
    anonymized: config.anonymizeData,
    consent_required: config.requireConsent,
    dnt_respected: config.respectDoNotTrack,
    timestamp: new Date().toISOString(),
  };

  return sanitized;
}

/**
 * Check if the current page should be tracked
 */
export function shouldTrackPage(
  pathname: string = window.location.pathname,
  excludedPaths: string[] = []
): boolean {
  // Check if current path is excluded
  return !excludedPaths.some(path => {
    if (path.endsWith('*')) {
      // Wildcard matching
      const prefix = path.slice(0, -1);
      return pathname.startsWith(prefix);
    }
    return pathname === path;
  });
}

/**
 * Get privacy-compliant user properties
 */
export function getPrivacyCompliantUserProperties(
  user: any,
  config: PrivacyConfig
): Record<string, any> {
  if (!user) {
    return {};
  }

  const properties: Record<string, any> = {
    user_id: user.id,
    user_type: user.type || 'registered',
  };

  // Only include additional data if anonymization is disabled
  if (!config.anonymizeData) {
    if (user.email) {
      properties.email = user.email;
    }
    if (user.phone) {
      properties.phone = user.phone;
    }
    if (user.first_name) {
      properties.first_name = user.first_name;
    }
    if (user.last_name) {
      properties.last_name = user.last_name;
    }
  }

  return properties;
}

/**
 * Create a privacy notice for Meta Pixel
 */
export function getPrivacyNotice(): string {
  return `
This website uses Meta Pixel to collect information about your interactions 
for analytics and advertising purposes. By continuing to use this site, you 
consent to the collection and use of this information in accordance with 
our Privacy Policy and Meta's Data Policy.

You can opt out of this tracking at any time through your privacy settings 
or by enabling "Do Not Track" in your browser.

For more information about how Meta processes your data, please visit:
https://www.facebook.com/privacy/policy/
  `.trim();
}

/**
 * Export consent data for GDPR compliance
 */
export function exportConsentData(): string {
  const consentData = getStoredConsent();
  const exportData = {
    meta_pixel_consent: consentData,
    export_timestamp: new Date().toISOString(),
    privacy_policy_version: '1.0',
  };

  return JSON.stringify(exportData, null, 2);
}

/**
 * Privacy compliance checker
 */
export class PrivacyComplianceChecker {
  private config: PrivacyConfig;

  constructor(config: PrivacyConfig) {
    this.config = config;
  }

  /**
   * Run all privacy compliance checks
   */
  checkCompliance(): {
    compliant: boolean;
    issues: string[];
    warnings: string[];
  } {
    const issues: string[] = [];
    const warnings: string[] = [];

    // Check Do Not Track compliance
    if (this.config.respectDoNotTrack && isDoNotTrackEnabled()) {
      warnings.push('Do Not Track is enabled - tracking should be disabled');
    }

    // Check consent requirement
    if (this.config.requireConsent) {
      const consent = getStoredConsent();
      if (!consent || !consent.granted) {
        issues.push('Consent is required but not granted');
      } else if (!isConsentValid(consent)) {
        issues.push('Stored consent is invalid or expired');
      }
    }

    // Check data retention
    if (this.config.dataRetentionDays <= 0) {
      warnings.push('Data retention period should be specified');
    }

    return {
      compliant: issues.length === 0,
      issues,
      warnings,
    };
  }
}
