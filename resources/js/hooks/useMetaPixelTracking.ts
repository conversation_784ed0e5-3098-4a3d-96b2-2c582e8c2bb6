import { useCallback } from 'react';
import { useMetaPixel } from '@/components/analytics/MetaPixelProvider';

interface UseMetaPixelTrackingReturn {
  // Basic tracking
  trackEvent: (eventName: string, eventData?: Record<string, any>) => void;
  trackPageView: () => void;
  
  // E-commerce tracking
  trackPurchase: (value: number, currency: string, contentIds?: string[]) => void;
  trackAddToCart: (contentIds: string[], value?: number, currency?: string) => void;
  trackInitiateCheckout: (value?: number, currency?: string, numItems?: number) => void;
  trackViewContent: (contentType: string, contentIds?: string[], contentName?: string) => void;
  
  // Lead generation
  trackLead: (value?: number, currency?: string) => void;
  trackCompleteRegistration: (contentName?: string, value?: number, currency?: string) => void;
  trackContact: () => void;
  
  // Engagement tracking
  trackSearch: (searchString: string, contentCategory?: string) => void;
  trackSubscribe: (value?: number, currency?: string) => void;
  
  // Utility functions
  generateEventId: () => string;
  isEnabled: boolean;
  hasConsent: boolean;
}

export function useMetaPixelTracking(): UseMetaPixelTrackingReturn {
  const metaPixel = useMetaPixel();

  // Basic tracking with automatic event ID generation
  const trackEvent = useCallback((
    eventName: string, 
    eventData: Record<string, any> = {}
  ) => {
    const eventId = metaPixel.generateEventId();
    metaPixel.trackEvent(eventName, eventData, eventId);
  }, [metaPixel]);

  const trackPageView = useCallback(() => {
    const eventId = metaPixel.generateEventId();
    metaPixel.trackPageView(eventId);
  }, [metaPixel]);

  // E-commerce tracking
  const trackPurchase = useCallback((
    value: number, 
    currency: string, 
    contentIds?: string[]
  ) => {
    const eventId = metaPixel.generateEventId();
    metaPixel.trackPurchase(value, currency, contentIds, eventId);
  }, [metaPixel]);

  const trackAddToCart = useCallback((
    contentIds: string[], 
    value?: number, 
    currency?: string
  ) => {
    const eventId = metaPixel.generateEventId();
    metaPixel.trackAddToCart(contentIds, value, currency, eventId);
  }, [metaPixel]);

  const trackInitiateCheckout = useCallback((
    value?: number, 
    currency?: string, 
    numItems?: number
  ) => {
    const eventId = metaPixel.generateEventId();
    metaPixel.trackInitiateCheckout(value, currency, numItems, eventId);
  }, [metaPixel]);

  const trackViewContent = useCallback((
    contentType: string, 
    contentIds?: string[], 
    contentName?: string
  ) => {
    const eventId = metaPixel.generateEventId();
    metaPixel.trackViewContent(contentType, contentIds, contentName, eventId);
  }, [metaPixel]);

  // Lead generation
  const trackLead = useCallback((value?: number, currency?: string) => {
    const eventId = metaPixel.generateEventId();
    metaPixel.trackLead(value, currency, eventId);
  }, [metaPixel]);

  const trackCompleteRegistration = useCallback((
    contentName?: string, 
    value?: number, 
    currency?: string
  ) => {
    const eventId = metaPixel.generateEventId();
    const eventData: Record<string, any> = {};
    if (contentName) eventData.content_name = contentName;
    if (value !== undefined) eventData.value = value;
    if (currency) eventData.currency = currency;
    metaPixel.trackEvent('CompleteRegistration', eventData, eventId);
  }, [metaPixel]);

  const trackContact = useCallback(() => {
    const eventId = metaPixel.generateEventId();
    metaPixel.trackEvent('Contact', {}, eventId);
  }, [metaPixel]);

  // Engagement tracking
  const trackSearch = useCallback((
    searchString: string, 
    contentCategory?: string
  ) => {
    const eventId = metaPixel.generateEventId();
    metaPixel.trackSearch(searchString, contentCategory, eventId);
  }, [metaPixel]);

  const trackSubscribe = useCallback((value?: number, currency?: string) => {
    const eventId = metaPixel.generateEventId();
    const eventData: Record<string, any> = {};
    if (value !== undefined) eventData.value = value;
    if (currency) eventData.currency = currency;
    metaPixel.trackEvent('Subscribe', eventData, eventId);
  }, [metaPixel]);

  return {
    // Basic tracking
    trackEvent,
    trackPageView,
    
    // E-commerce tracking
    trackPurchase,
    trackAddToCart,
    trackInitiateCheckout,
    trackViewContent,
    
    // Lead generation
    trackLead,
    trackCompleteRegistration,
    trackContact,
    
    // Engagement tracking
    trackSearch,
    trackSubscribe,
    
    // Utility functions
    generateEventId: metaPixel.generateEventId,
    isEnabled: metaPixel.config.enabled,
    hasConsent: metaPixel.hasConsent,
  };
}
