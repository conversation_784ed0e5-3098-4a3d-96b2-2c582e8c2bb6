<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meta_pixel_configs', function (Blueprint $table) {
            $table->id();

            // Basic Configuration
            $table->boolean('enabled')->default(false);
            $table->string('pixel_id')->nullable();
            $table->text('access_token')->nullable(); // For Conversions API

            // Conversions API Configuration
            $table->boolean('conversions_api_enabled')->default(false);
            $table->string('test_event_code')->nullable(); // For testing events

            // Event Configuration
            $table->json('enabled_events')->nullable(); // Which events to track
            $table->json('custom_events')->nullable(); // Custom event definitions

            // Privacy & Compliance
            $table->boolean('respect_do_not_track')->default(true);
            $table->boolean('require_consent')->default(true);
            $table->json('consent_settings')->nullable(); // Default consent settings

            // Advanced Settings
            $table->boolean('debug_mode')->default(false);
            $table->boolean('automatic_matching')->default(true); // Advanced matching
            $table->json('custom_parameters')->nullable(); // Additional custom parameters

            // Deduplication Settings
            $table->boolean('enable_deduplication')->default(true);
            $table->string('deduplication_method')->default('event_id'); // event_id, fbp_fbc, custom

            // Performance Settings
            $table->boolean('lazy_load')->default(false);
            $table->integer('event_batch_size')->default(1);
            $table->integer('event_delay_ms')->default(0);

            $table->timestamps();

            // Indexes for performance
            $table->index('enabled');
            $table->index('conversions_api_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meta_pixel_configs');
    }
};
