<?php

namespace Tests\Feature\Admin;

use Tests\TestCase;
use App\Models\User;
use App\Models\MetaPixelConfig;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Inertia\Testing\AssertableInertia as Assert;

class MetaPixelControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
        ]);
    }

    public function test_index_displays_meta_pixel_configuration_page()
    {
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.meta-pixel.index'));

        $response->assertStatus(200)
            ->assertInertia(fn (Assert $page) => 
                $page->component('admin/MetaPixel/Index')
                    ->has('config')
                    ->has('standardEvents')
            );
    }

    public function test_index_includes_existing_configuration()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'debug_mode' => true,
            'respect_do_not_track' => false,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.meta-pixel.index'));

        $response->assertStatus(200)
            ->assertInertia(fn (Assert $page) => 
                $page->where('config.enabled', true)
                    ->where('config.pixel_id', 'test_pixel_123')
                    ->where('config.debug_mode', true)
                    ->where('config.respect_do_not_track', false)
            );
    }

    public function test_store_creates_new_configuration()
    {
        $configData = [
            'enabled' => true,
            'pixel_id' => 'new_pixel_123',
            'access_token' => 'new_access_token',
            'conversions_api_enabled' => true,
            'debug_mode' => false,
            'respect_do_not_track' => true,
            'require_consent' => true,
            'automatic_matching' => false,
            'enable_deduplication' => true,
            'lazy_load' => false,
            'event_delay_ms' => 500,
            'enabled_events' => ['PageView', 'Purchase'],
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.store'), $configData);

        $response->assertRedirect(route('admin.meta-pixel.index'));

        $this->assertDatabaseHas('meta_pixel_configs', [
            'enabled' => true,
            'pixel_id' => 'new_pixel_123',
            'conversions_api_enabled' => true,
            'debug_mode' => false,
            'respect_do_not_track' => true,
            'require_consent' => true,
            'automatic_matching' => false,
            'enable_deduplication' => true,
            'lazy_load' => false,
            'event_delay_ms' => 500,
        ]);
    }

    public function test_store_updates_existing_configuration()
    {
        $existingConfig = MetaPixelConfig::create([
            'enabled' => false,
            'pixel_id' => 'old_pixel_123',
        ]);

        $updateData = [
            'enabled' => true,
            'pixel_id' => 'updated_pixel_456',
            'debug_mode' => true,
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.store'), $updateData);

        $response->assertRedirect(route('admin.meta-pixel.index'));

        $existingConfig->refresh();
        $this->assertTrue($existingConfig->enabled);
        $this->assertEquals('updated_pixel_456', $existingConfig->pixel_id);
        $this->assertTrue($existingConfig->debug_mode);
    }

    public function test_store_validates_required_fields()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.store'), [
                'enabled' => true,
                // Missing pixel_id
            ]);

        $response->assertSessionHasErrors(['pixel_id']);
    }

    public function test_store_validates_pixel_id_format()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.store'), [
                'enabled' => true,
                'pixel_id' => 'invalid-pixel-id-with-special-chars!',
            ]);

        $response->assertSessionHasErrors(['pixel_id']);
    }

    public function test_store_validates_access_token_when_conversions_api_enabled()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.store'), [
                'enabled' => true,
                'pixel_id' => 'valid_pixel_123',
                'conversions_api_enabled' => true,
                // Missing access_token
            ]);

        $response->assertSessionHasErrors(['access_token']);
    }

    public function test_store_validates_event_delay_ms_range()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.store'), [
                'enabled' => true,
                'pixel_id' => 'valid_pixel_123',
                'event_delay_ms' => -100, // Invalid negative value
            ]);

        $response->assertSessionHasErrors(['event_delay_ms']);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.store'), [
                'enabled' => true,
                'pixel_id' => 'valid_pixel_123',
                'event_delay_ms' => 60000, // Invalid too large value
            ]);

        $response->assertSessionHasErrors(['event_delay_ms']);
    }

    public function test_test_configuration_returns_success_for_valid_config()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => false,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.test'));

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'results' => [
                    'pixel_configuration' => [
                        'success' => true,
                    ],
                ],
            ]);
    }

    public function test_test_configuration_includes_conversions_api_test()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.test'));

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'results' => [
                    'pixel_configuration' => [
                        'success' => true,
                    ],
                    'conversions_api' => [
                        'success' => true,
                    ],
                ],
            ]);
    }

    public function test_test_configuration_returns_failure_for_invalid_config()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => '', // Invalid empty pixel ID
        ]);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.test'));

        $response->assertStatus(200)
            ->assertJson([
                'success' => false,
                'results' => [
                    'pixel_configuration' => [
                        'success' => false,
                    ],
                ],
            ]);
    }

    public function test_send_test_event_sends_conversions_api_event()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.send-test-event'), [
                'event_name' => 'PageView',
                'event_data' => ['test' => 'data'],
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Test event sent successfully',
            ]);

        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'graph.facebook.com') &&
                   $request->hasHeader('Authorization');
        });
    }

    public function test_send_test_event_fails_when_conversions_api_disabled()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => false,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.send-test-event'), [
                'event_name' => 'PageView',
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Conversions API is not enabled',
            ]);
    }

    public function test_clear_cache_clears_meta_pixel_caches()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
        ]);

        // Populate cache
        MetaPixelConfig::current();

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.clear-cache'));

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Cache cleared successfully',
            ]);
    }

    public function test_unauthorized_user_cannot_access_meta_pixel_admin()
    {
        $regularUser = User::factory()->create(['is_admin' => false]);

        $response = $this->actingAs($regularUser)
            ->get(route('admin.meta-pixel.index'));

        $response->assertStatus(403);
    }

    public function test_guest_user_cannot_access_meta_pixel_admin()
    {
        $response = $this->get(route('admin.meta-pixel.index'));

        $response->assertRedirect(route('login'));
    }

    public function test_store_sanitizes_input_data()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.meta-pixel.store'), [
                'enabled' => true,
                'pixel_id' => '  test_pixel_123  ', // With whitespace
                'access_token' => '  token_with_spaces  ',
                'event_delay_ms' => '1000', // String number
            ]);

        $response->assertRedirect(route('admin.meta-pixel.index'));

        $config = MetaPixelConfig::first();
        $this->assertEquals('test_pixel_123', $config->pixel_id); // Trimmed
        $this->assertEquals('token_with_spaces', $config->access_token); // Trimmed
        $this->assertIsInt($config->event_delay_ms); // Cast to integer
        $this->assertEquals(1000, $config->event_delay_ms);
    }
}
