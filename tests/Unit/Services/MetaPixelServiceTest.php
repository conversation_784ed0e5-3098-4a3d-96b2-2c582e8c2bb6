<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\MetaPixelService;
use App\Models\MetaPixelConfig;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

class MetaPixelServiceTest extends TestCase
{
    use RefreshDatabase;

    private MetaPixelService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(MetaPixelService::class);
    }

    public function test_is_enabled_returns_false_when_no_config_exists()
    {
        $this->assertFalse($this->service->isEnabled());
    }

    public function test_is_enabled_returns_true_when_config_is_enabled()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_id',
        ]);

        $this->assertTrue($this->service->isEnabled());
    }

    public function test_is_enabled_returns_false_when_config_is_disabled()
    {
        MetaPixelConfig::create([
            'enabled' => false,
            'pixel_id' => 'test_pixel_id',
        ]);

        $this->assertFalse($this->service->isEnabled());
    }

    public function test_get_pixel_id_returns_null_when_no_config()
    {
        $this->assertNull($this->service->getPixelId());
    }

    public function test_get_pixel_id_returns_pixel_id_when_config_exists()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
        ]);

        $this->assertEquals('test_pixel_123', $this->service->getPixelId());
    }

    public function test_is_conversions_api_enabled_returns_false_by_default()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_id',
        ]);

        $this->assertFalse($this->service->isConversionsApiEnabled());
    }

    public function test_is_conversions_api_enabled_returns_true_when_enabled()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_id',
            'conversions_api_enabled' => true,
            'access_token' => 'test_token',
        ]);

        $this->assertTrue($this->service->isConversionsApiEnabled());
    }

    public function test_get_frontend_config_returns_default_when_no_config()
    {
        $config = $this->service->getFrontendConfig();

        $this->assertIsArray($config);
        $this->assertFalse($config['enabled']);
        $this->assertNull($config['pixel_id']);
    }

    public function test_get_frontend_config_returns_cached_config()
    {
        $expectedConfig = [
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'debug_mode' => false,
            'respect_do_not_track' => true,
            'require_consent' => true,
            'automatic_matching' => false,
            'enable_deduplication' => true,
            'lazy_load' => false,
            'event_delay_ms' => 0,
            'enabled_events' => ['PageView', 'Purchase'],
        ];

        Cache::put('meta_pixel_frontend_config', $expectedConfig, 3600);

        $config = $this->service->getFrontendConfig();

        $this->assertEquals($expectedConfig, $config);
    }

    public function test_get_frontend_config_generates_config_from_database()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_456',
            'debug_mode' => true,
            'respect_do_not_track' => false,
            'require_consent' => false,
            'automatic_matching' => true,
            'enable_deduplication' => false,
            'lazy_load' => true,
            'event_delay_ms' => 1000,
            'enabled_events' => ['PageView', 'Purchase', 'Lead'],
        ]);

        $config = $this->service->getFrontendConfig();

        $this->assertTrue($config['enabled']);
        $this->assertEquals('test_pixel_456', $config['pixel_id']);
        $this->assertTrue($config['debug_mode']);
        $this->assertFalse($config['respect_do_not_track']);
        $this->assertFalse($config['require_consent']);
        $this->assertTrue($config['automatic_matching']);
        $this->assertFalse($config['enable_deduplication']);
        $this->assertTrue($config['lazy_load']);
        $this->assertEquals(1000, $config['event_delay_ms']);
        $this->assertEquals(['PageView', 'Purchase', 'Lead'], $config['enabled_events']);
    }

    public function test_validate_event_data_returns_true_for_valid_data()
    {
        $validData = [
            'value' => 99.99,
            'currency' => 'USD',
            'content_ids' => ['product_123'],
        ];

        $this->assertTrue($this->service->validateEventData('Purchase', $validData));
    }

    public function test_validate_event_data_returns_false_for_invalid_currency()
    {
        $invalidData = [
            'value' => 99.99,
            'currency' => 'INVALID',
            'content_ids' => ['product_123'],
        ];

        $this->assertFalse($this->service->validateEventData('Purchase', $invalidData));
    }

    public function test_validate_event_data_returns_false_for_negative_value()
    {
        $invalidData = [
            'value' => -10.00,
            'currency' => 'USD',
        ];

        $this->assertFalse($this->service->validateEventData('Purchase', $invalidData));
    }

    public function test_get_standard_events_returns_configured_events()
    {
        Config::set('meta-pixel.standard_events', [
            'PageView' => ['description' => 'Page view'],
            'Purchase' => ['description' => 'Purchase'],
        ]);

        $events = $this->service->getStandardEvents();

        $this->assertIsArray($events);
        $this->assertArrayHasKey('PageView', $events);
        $this->assertArrayHasKey('Purchase', $events);
    }

    public function test_get_default_consent_settings_returns_configured_settings()
    {
        $expectedSettings = [
            'ad_storage' => 'denied',
            'analytics_storage' => 'denied',
            'ad_user_data' => 'denied',
            'ad_personalization' => 'denied',
        ];

        Config::set('meta-pixel.default_consent_settings', $expectedSettings);

        $settings = $this->service->getDefaultConsentSettings();

        $this->assertEquals($expectedSettings, $settings);
    }

    public function test_clear_cache_clears_all_related_caches()
    {
        // Set up cache data
        Cache::put('meta_pixel_frontend_config', ['test' => 'data'], 3600);
        MetaPixelConfig::create(['enabled' => true, 'pixel_id' => 'test']);

        // Verify cache exists
        $this->assertTrue(Cache::has('meta_pixel_frontend_config'));

        // Clear cache
        $this->service->clearCache();

        // Verify cache is cleared
        $this->assertFalse(Cache::has('meta_pixel_frontend_config'));
    }

    public function test_test_configuration_returns_results_array()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_id',
            'conversions_api_enabled' => false,
        ]);

        $results = $this->service->testConfiguration();

        $this->assertIsArray($results);
        $this->assertArrayHasKey('pixel_configuration', $results);
        $this->assertTrue($results['pixel_configuration']['success']);
    }

    public function test_test_configuration_fails_when_no_pixel_id()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => '',
        ]);

        $results = $this->service->testConfiguration();

        $this->assertFalse($results['pixel_configuration']['success']);
        $this->assertStringContainsString('Pixel ID is required', $results['pixel_configuration']['message']);
    }

    public function test_process_user_data_hashes_sensitive_information()
    {
        $userData = [
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'city' => 'New York',
        ];

        $processedData = $this->service->processUserData($userData);

        // Check that sensitive data is hashed
        $this->assertNotEquals('<EMAIL>', $processedData['email']);
        $this->assertNotEquals('+1234567890', $processedData['phone']);
        $this->assertNotEquals('John', $processedData['first_name']);
        $this->assertNotEquals('Doe', $processedData['last_name']);

        // Check that hashed values are strings
        $this->assertIsString($processedData['email']);
        $this->assertIsString($processedData['phone']);
        $this->assertIsString($processedData['first_name']);
        $this->assertIsString($processedData['last_name']);

        // Non-sensitive data should remain unchanged
        $this->assertEquals('New York', $processedData['city']);
    }

    public function test_process_user_data_handles_empty_data()
    {
        $processedData = $this->service->processUserData([]);

        $this->assertIsArray($processedData);
        $this->assertEmpty($processedData);
    }

    public function test_process_user_data_handles_null_values()
    {
        $userData = [
            'email' => null,
            'phone' => '',
            'first_name' => 'John',
        ];

        $processedData = $this->service->processUserData($userData);

        $this->assertArrayNotHasKey('email', $processedData);
        $this->assertArrayNotHasKey('phone', $processedData);
        $this->assertArrayHasKey('first_name', $processedData);
    }
}
