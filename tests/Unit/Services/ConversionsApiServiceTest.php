<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ConversionsApiService;
use App\Models\MetaPixelConfig;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ConversionsApiServiceTest extends TestCase
{
    use RefreshDatabase;

    private ConversionsApiService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(ConversionsApiService::class);
    }

    public function test_send_event_returns_false_when_not_enabled()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_id',
            'conversions_api_enabled' => false,
        ]);

        $result = $this->service->sendEvent('PageView', []);

        $this->assertFalse($result);
    }

    public function test_send_event_returns_false_when_no_config()
    {
        $result = $this->service->sendEvent('PageView', []);

        $this->assertFalse($result);
    }

    public function test_send_event_makes_http_request_when_enabled()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $result = $this->service->sendEvent('PageView', [
            'event_time' => time(),
            'action_source' => 'website',
        ]);

        $this->assertTrue($result);

        Http::assertSent(function ($request) {
            return $request->url() === 'https://graph.facebook.com/v18.0/test_pixel_123/events' &&
                   $request->hasHeader('Authorization', 'Bearer test_access_token');
        });
    }

    public function test_send_event_handles_api_error_gracefully()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['error' => 'Invalid token'], 400)
        ]);

        Log::shouldReceive('error')->once();

        $result = $this->service->sendEvent('PageView', []);

        $this->assertFalse($result);
    }

    public function test_send_event_includes_required_fields()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $this->service->sendEvent('PageView', [
            'event_time' => 1234567890,
            'action_source' => 'website',
        ]);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $event = $data['data'][0];

            return $event['event_name'] === 'PageView' &&
                   $event['event_time'] === 1234567890 &&
                   $event['action_source'] === 'website' &&
                   isset($event['event_id']);
        });
    }

    public function test_send_purchase_event_includes_purchase_data()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'John Doe',
        ]);

        $result = $this->service->sendPurchaseEvent(
            value: 99.99,
            currency: 'USD',
            contentIds: ['product_123'],
            user: $user
        );

        $this->assertTrue($result);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $event = $data['data'][0];

            return $event['event_name'] === 'Purchase' &&
                   $event['custom_data']['value'] === 99.99 &&
                   $event['custom_data']['currency'] === 'USD' &&
                   $event['custom_data']['content_ids'] === ['product_123'] &&
                   isset($event['user_data']['email']);
        });
    }

    public function test_send_lead_event_includes_lead_data()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $result = $this->service->sendLeadEvent(
            value: 50.00,
            currency: 'EUR'
        );

        $this->assertTrue($result);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $event = $data['data'][0];

            return $event['event_name'] === 'Lead' &&
                   $event['custom_data']['value'] === 50.00 &&
                   $event['custom_data']['currency'] === 'EUR';
        });
    }

    public function test_send_search_event_includes_search_data()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $result = $this->service->sendSearchEvent(
            searchString: 'mobile phone',
            contentCategory: 'electronics'
        );

        $this->assertTrue($result);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $event = $data['data'][0];

            return $event['event_name'] === 'Search' &&
                   $event['custom_data']['search_string'] === 'mobile phone' &&
                   $event['custom_data']['content_category'] === 'electronics';
        });
    }

    public function test_test_connection_returns_success_when_api_responds()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['events_received' => 1], 200)
        ]);

        $result = $this->service->testConnection();

        $this->assertTrue($result['success']);
        $this->assertStringContainsString('successfully', $result['message']);
    }

    public function test_test_connection_returns_failure_when_api_errors()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'invalid_token',
        ]);

        Http::fake([
            'graph.facebook.com/*' => Http::response(['error' => 'Invalid token'], 400)
        ]);

        $result = $this->service->testConnection();

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('failed', $result['message']);
    }

    public function test_test_connection_returns_failure_when_not_configured()
    {
        $result = $this->service->testConnection();

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('not configured', $result['message']);
    }

    public function test_create_event_data_includes_deduplication_id()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
            'enable_deduplication' => true,
        ]);

        $eventData = $this->service->createEventData('PageView', [], null, 'test_event_id_123');

        $this->assertEquals('test_event_id_123', $eventData['event_id']);
    }

    public function test_create_event_data_excludes_deduplication_when_disabled()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
            'enable_deduplication' => false,
        ]);

        $eventData = $this->service->createEventData('PageView', [], null, 'test_event_id_123');

        $this->assertArrayNotHasKey('event_id', $eventData);
    }

    public function test_create_user_data_hashes_sensitive_information()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'John Doe',
        ]);

        $userData = $this->service->createUserData($user);

        $this->assertNotEquals('<EMAIL>', $userData['email']);
        $this->assertIsString($userData['email']);
        $this->assertEquals(64, strlen($userData['email'])); // SHA256 hash length
    }

    public function test_create_user_data_handles_null_user()
    {
        $userData = $this->service->createUserData(null);

        $this->assertIsArray($userData);
        $this->assertEmpty($userData);
    }

    public function test_hash_user_data_produces_consistent_hashes()
    {
        $data = '<EMAIL>';
        
        $hash1 = $this->service->hashUserData($data);
        $hash2 = $this->service->hashUserData($data);

        $this->assertEquals($hash1, $hash2);
        $this->assertEquals(64, strlen($hash1)); // SHA256 hash length
    }

    public function test_hash_user_data_handles_empty_string()
    {
        $hash = $this->service->hashUserData('');

        $this->assertIsString($hash);
        $this->assertEquals(64, strlen($hash));
    }
}
