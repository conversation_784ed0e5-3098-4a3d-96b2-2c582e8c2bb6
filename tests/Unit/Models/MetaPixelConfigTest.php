<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\MetaPixelConfig;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

class MetaPixelConfigTest extends TestCase
{
    use RefreshDatabase;

    public function test_current_returns_null_when_no_config_exists()
    {
        $config = MetaPixelConfig::current();

        $this->assertNull($config);
    }

    public function test_current_returns_first_config_when_exists()
    {
        $expectedConfig = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
        ]);

        $config = MetaPixelConfig::current();

        $this->assertNotNull($config);
        $this->assertEquals($expectedConfig->id, $config->id);
        $this->assertEquals('test_pixel_123', $config->pixel_id);
    }

    public function test_current_caches_result()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
        ]);

        // First call should hit database
        $config1 = MetaPixelConfig::current();
        
        // Second call should hit cache
        $config2 = MetaPixelConfig::current();

        $this->assertEquals($config1->id, $config2->id);
        $this->assertTrue(Cache::has('meta_pixel_config'));
    }

    public function test_clear_cache_removes_cached_config()
    {
        MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
        ]);

        // Cache the config
        MetaPixelConfig::current();
        $this->assertTrue(Cache::has('meta_pixel_config'));

        // Clear cache
        MetaPixelConfig::clearCache();
        $this->assertFalse(Cache::has('meta_pixel_config'));
    }

    public function test_enabled_events_cast_to_array()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'enabled_events' => ['PageView', 'Purchase', 'Lead'],
        ]);

        $this->assertIsArray($config->enabled_events);
        $this->assertEquals(['PageView', 'Purchase', 'Lead'], $config->enabled_events);
    }

    public function test_consent_settings_cast_to_array()
    {
        $consentSettings = [
            'ad_storage' => 'denied',
            'analytics_storage' => 'granted',
        ];

        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'consent_settings' => $consentSettings,
        ]);

        $this->assertIsArray($config->consent_settings);
        $this->assertEquals($consentSettings, $config->consent_settings);
    }

    public function test_deduplication_settings_cast_to_array()
    {
        $deduplicationSettings = [
            'cookie_name' => '_fbp',
            'parameter_name' => 'fbclid',
        ];

        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'deduplication_settings' => $deduplicationSettings,
        ]);

        $this->assertIsArray($config->deduplication_settings);
        $this->assertEquals($deduplicationSettings, $config->deduplication_settings);
    }

    public function test_is_valid_returns_true_for_valid_config()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
        ]);

        $this->assertTrue($config->isValid());
    }

    public function test_is_valid_returns_false_when_disabled()
    {
        $config = MetaPixelConfig::create([
            'enabled' => false,
            'pixel_id' => 'test_pixel_123',
        ]);

        $this->assertFalse($config->isValid());
    }

    public function test_is_valid_returns_false_when_no_pixel_id()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => '',
        ]);

        $this->assertFalse($config->isValid());
    }

    public function test_is_conversions_api_ready_returns_true_when_configured()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => 'test_access_token',
        ]);

        $this->assertTrue($config->isConversionsApiReady());
    }

    public function test_is_conversions_api_ready_returns_false_when_disabled()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => false,
            'access_token' => 'test_access_token',
        ]);

        $this->assertFalse($config->isConversionsApiReady());
    }

    public function test_is_conversions_api_ready_returns_false_when_no_token()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'conversions_api_enabled' => true,
            'access_token' => '',
        ]);

        $this->assertFalse($config->isConversionsApiReady());
    }

    public function test_get_frontend_config_returns_array()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'debug_mode' => true,
            'respect_do_not_track' => false,
            'require_consent' => true,
            'automatic_matching' => true,
            'enable_deduplication' => false,
            'lazy_load' => true,
            'event_delay_ms' => 1000,
            'enabled_events' => ['PageView', 'Purchase'],
        ]);

        $frontendConfig = $config->getFrontendConfig();

        $this->assertIsArray($frontendConfig);
        $this->assertTrue($frontendConfig['enabled']);
        $this->assertEquals('test_pixel_123', $frontendConfig['pixel_id']);
        $this->assertTrue($frontendConfig['debug_mode']);
        $this->assertFalse($frontendConfig['respect_do_not_track']);
        $this->assertTrue($frontendConfig['require_consent']);
        $this->assertTrue($frontendConfig['automatic_matching']);
        $this->assertFalse($frontendConfig['enable_deduplication']);
        $this->assertTrue($frontendConfig['lazy_load']);
        $this->assertEquals(1000, $frontendConfig['event_delay_ms']);
        $this->assertEquals(['PageView', 'Purchase'], $frontendConfig['enabled_events']);
    }

    public function test_get_frontend_config_excludes_sensitive_data()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'access_token' => 'sensitive_token',
        ]);

        $frontendConfig = $config->getFrontendConfig();

        $this->assertArrayNotHasKey('access_token', $frontendConfig);
        $this->assertArrayNotHasKey('id', $frontendConfig);
        $this->assertArrayNotHasKey('created_at', $frontendConfig);
        $this->assertArrayNotHasKey('updated_at', $frontendConfig);
    }

    public function test_model_clears_cache_on_save()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
        ]);

        // Cache the config
        MetaPixelConfig::current();
        $this->assertTrue(Cache::has('meta_pixel_config'));

        // Update the config
        $config->update(['pixel_id' => 'updated_pixel_456']);

        // Cache should be cleared
        $this->assertFalse(Cache::has('meta_pixel_config'));
    }

    public function test_model_clears_cache_on_delete()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
        ]);

        // Cache the config
        MetaPixelConfig::current();
        $this->assertTrue(Cache::has('meta_pixel_config'));

        // Delete the config
        $config->delete();

        // Cache should be cleared
        $this->assertFalse(Cache::has('meta_pixel_config'));
    }

    public function test_fillable_attributes_are_mass_assignable()
    {
        $attributes = [
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'access_token' => 'test_token',
            'conversions_api_enabled' => true,
            'debug_mode' => true,
            'respect_do_not_track' => false,
            'require_consent' => true,
            'automatic_matching' => true,
            'enable_deduplication' => false,
            'lazy_load' => true,
            'event_delay_ms' => 1000,
            'enabled_events' => ['PageView'],
            'consent_settings' => ['ad_storage' => 'denied'],
            'deduplication_settings' => ['cookie_name' => '_fbp'],
        ];

        $config = MetaPixelConfig::create($attributes);

        foreach ($attributes as $key => $value) {
            $this->assertEquals($value, $config->$key);
        }
    }

    public function test_boolean_attributes_are_cast_correctly()
    {
        $config = MetaPixelConfig::create([
            'enabled' => '1',
            'conversions_api_enabled' => '0',
            'debug_mode' => 'true',
            'respect_do_not_track' => 'false',
            'require_consent' => 1,
            'automatic_matching' => 0,
            'enable_deduplication' => true,
            'lazy_load' => false,
        ]);

        $this->assertIsBool($config->enabled);
        $this->assertIsBool($config->conversions_api_enabled);
        $this->assertIsBool($config->debug_mode);
        $this->assertIsBool($config->respect_do_not_track);
        $this->assertIsBool($config->require_consent);
        $this->assertIsBool($config->automatic_matching);
        $this->assertIsBool($config->enable_deduplication);
        $this->assertIsBool($config->lazy_load);

        $this->assertTrue($config->enabled);
        $this->assertFalse($config->conversions_api_enabled);
    }

    public function test_integer_attributes_are_cast_correctly()
    {
        $config = MetaPixelConfig::create([
            'enabled' => true,
            'pixel_id' => 'test_pixel_123',
            'event_delay_ms' => '1500',
        ]);

        $this->assertIsInt($config->event_delay_ms);
        $this->assertEquals(1500, $config->event_delay_ms);
    }
}
