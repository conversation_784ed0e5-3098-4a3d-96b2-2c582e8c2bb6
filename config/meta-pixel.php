<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Meta Pixel Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Meta Pixel (Facebook Pixel) integration including
    | both client-side JavaScript tracking and server-side Conversions API.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Settings
    |--------------------------------------------------------------------------
    |
    | Default configuration values that will be used when creating new
    | Meta Pixel configurations or as fallback values.
    |
    */
    'defaults' => [
        'enabled' => env('META_PIXEL_ENABLED', false),
        'pixel_id' => env('META_PIXEL_ID'),
        'access_token' => env('META_PIXEL_ACCESS_TOKEN'),
        'conversions_api_enabled' => env('META_CONVERSIONS_API_ENABLED', false),
        'test_event_code' => env('META_PIXEL_TEST_EVENT_CODE'),
        'debug_mode' => env('META_PIXEL_DEBUG', false),
        'respect_do_not_track' => env('META_PIXEL_RESPECT_DNT', true),
        'require_consent' => env('META_PIXEL_REQUIRE_CONSENT', true),
        'automatic_matching' => env('META_PIXEL_AUTOMATIC_MATCHING', true),
        'enable_deduplication' => env('META_PIXEL_ENABLE_DEDUPLICATION', true),
        'deduplication_method' => env('META_PIXEL_DEDUPLICATION_METHOD', 'event_id'),
        'lazy_load' => env('META_PIXEL_LAZY_LOAD', false),
        'event_batch_size' => env('META_PIXEL_EVENT_BATCH_SIZE', 1),
        'event_delay_ms' => env('META_PIXEL_EVENT_DELAY_MS', 0),
    ],

    /*
    |--------------------------------------------------------------------------
    | Standard Events
    |--------------------------------------------------------------------------
    |
    | List of standard Meta Pixel events that can be tracked.
    | These are predefined by Meta and have specific purposes.
    |
    */
    'standard_events' => [
        'PageView' => [
            'name' => 'PageView',
            'description' => 'Track page views',
            'parameters' => [],
            'automatic' => true,
        ],
        'ViewContent' => [
            'name' => 'ViewContent',
            'description' => 'Track when someone views content',
            'parameters' => ['content_type', 'content_ids', 'content_name', 'content_category', 'value', 'currency'],
            'automatic' => false,
        ],
        'Search' => [
            'name' => 'Search',
            'description' => 'Track searches on your website',
            'parameters' => ['search_string', 'content_category', 'content_ids'],
            'automatic' => false,
        ],
        'AddToCart' => [
            'name' => 'AddToCart',
            'description' => 'Track when items are added to cart',
            'parameters' => ['content_ids', 'content_type', 'value', 'currency'],
            'automatic' => false,
        ],
        'AddToWishlist' => [
            'name' => 'AddToWishlist',
            'description' => 'Track when items are added to wishlist',
            'parameters' => ['content_ids', 'content_type', 'value', 'currency'],
            'automatic' => false,
        ],
        'InitiateCheckout' => [
            'name' => 'InitiateCheckout',
            'description' => 'Track when checkout is initiated',
            'parameters' => ['content_ids', 'content_type', 'value', 'currency', 'num_items'],
            'automatic' => false,
        ],
        'AddPaymentInfo' => [
            'name' => 'AddPaymentInfo',
            'description' => 'Track when payment info is added',
            'parameters' => ['content_ids', 'content_type', 'value', 'currency'],
            'automatic' => false,
        ],
        'Purchase' => [
            'name' => 'Purchase',
            'description' => 'Track purchases',
            'parameters' => ['content_ids', 'content_type', 'value', 'currency', 'num_items'],
            'automatic' => false,
        ],
        'Lead' => [
            'name' => 'Lead',
            'description' => 'Track lead generation',
            'parameters' => ['content_name', 'content_category', 'value', 'currency'],
            'automatic' => false,
        ],
        'CompleteRegistration' => [
            'name' => 'CompleteRegistration',
            'description' => 'Track completed registrations',
            'parameters' => ['content_name', 'value', 'currency', 'status'],
            'automatic' => false,
        ],
        'Contact' => [
            'name' => 'Contact',
            'description' => 'Track contact form submissions',
            'parameters' => [],
            'automatic' => false,
        ],
        'CustomizeProduct' => [
            'name' => 'CustomizeProduct',
            'description' => 'Track product customizations',
            'parameters' => ['content_ids', 'content_type', 'value', 'currency'],
            'automatic' => false,
        ],
        'Donate' => [
            'name' => 'Donate',
            'description' => 'Track donations',
            'parameters' => ['value', 'currency'],
            'automatic' => false,
        ],
        'FindLocation' => [
            'name' => 'FindLocation',
            'description' => 'Track location searches',
            'parameters' => [],
            'automatic' => false,
        ],
        'Schedule' => [
            'name' => 'Schedule',
            'description' => 'Track appointment scheduling',
            'parameters' => [],
            'automatic' => false,
        ],
        'StartTrial' => [
            'name' => 'StartTrial',
            'description' => 'Track trial starts',
            'parameters' => ['value', 'currency', 'predicted_ltv'],
            'automatic' => false,
        ],
        'SubmitApplication' => [
            'name' => 'SubmitApplication',
            'description' => 'Track application submissions',
            'parameters' => [],
            'automatic' => false,
        ],
        'Subscribe' => [
            'name' => 'Subscribe',
            'description' => 'Track subscriptions',
            'parameters' => ['value', 'currency', 'predicted_ltv'],
            'automatic' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Consent Settings
    |--------------------------------------------------------------------------
    |
    | Default consent mode settings for GDPR compliance.
    | These settings control what data can be collected before user consent.
    |
    */
    'default_consent_settings' => [
        'ad_storage' => 'denied',
        'analytics_storage' => 'denied',
        'ad_user_data' => 'denied',
        'ad_personalization' => 'denied',
        'functionality_storage' => 'granted',
        'security_storage' => 'granted',
    ],

    /*
    |--------------------------------------------------------------------------
    | Conversions API Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for Meta Conversions API (server-side events).
    |
    */
    'conversions_api' => [
        'endpoint' => 'https://graph.facebook.com/v21.0',
        'timeout' => 30,
        'retry_attempts' => 3,
        'retry_delay' => 1000, // milliseconds
        'batch_size' => 1000,
        'rate_limit' => [
            'requests_per_hour' => 1000000,
            'events_per_request' => 1000,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Deduplication Settings
    |--------------------------------------------------------------------------
    |
    | Settings for deduplicating events between browser and server.
    |
    */
    'deduplication' => [
        'methods' => [
            'event_id' => 'Event ID based deduplication',
            'fbp_fbc' => 'Facebook browser cookie based deduplication',
            'custom' => 'Custom deduplication logic',
        ],
        'default_method' => 'event_id',
        'event_id_ttl' => 86400, // 24 hours in seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Privacy Settings
    |--------------------------------------------------------------------------
    |
    | Privacy and compliance related settings.
    |
    */
    'privacy' => [
        'respect_do_not_track' => true,
        'data_retention_days' => 730, // 2 years
        'anonymize_ip' => true,
        'hash_user_data' => true,
        'exclude_user_agents' => [
            'bot',
            'crawler',
            'spider',
            'scraper',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Settings to optimize performance and loading.
    |
    */
    'performance' => [
        'lazy_load' => false,
        'preload_script' => true,
        'async_loading' => true,
        'cache_ttl' => 3600, // 1 hour
        'queue_events' => false,
        'batch_events' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Debug Settings
    |--------------------------------------------------------------------------
    |
    | Settings for debugging and development.
    |
    */
    'debug' => [
        'enabled' => env('META_PIXEL_DEBUG', false),
        'log_events' => env('META_PIXEL_LOG_EVENTS', false),
        'console_logging' => env('META_PIXEL_CONSOLE_LOGGING', false),
        'test_mode' => env('META_PIXEL_TEST_MODE', false),
    ],
];
