<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\MetaPixelService;
use App\Services\ConversionsApiService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class MetaPixelController extends Controller
{
    protected MetaPixelService $metaPixelService;
    protected ConversionsApiService $conversionsApiService;

    public function __construct(
        MetaPixelService $metaPixelService,
        ConversionsApiService $conversionsApiService
    ) {
        $this->metaPixelService = $metaPixelService;
        $this->conversionsApiService = $conversionsApiService;
    }

    /**
     * Display Meta Pixel configuration dashboard.
     */
    public function index(): Response
    {
        try {
            $config = $this->metaPixelService->getConfig();
            $status = $this->metaPixelService->getStatus();
            $conversionsApiStatus = $this->conversionsApiService->getStatus();
            $standardEvents = $this->metaPixelService->getStandardEvents();

            return Inertia::render('admin/MetaPixel/Index', [
                'config' => [
                    'enabled' => $config->enabled,
                    'pixel_id' => $config->pixel_id,
                    'access_token_masked' => $config->getMaskedAccessToken(),
                    'conversions_api_enabled' => $config->conversions_api_enabled,
                    'test_event_code' => $config->test_event_code,
                    'enabled_events' => $config->enabled_events ?? [],
                    'custom_events' => $config->custom_events ?? [],
                    'respect_do_not_track' => $config->respect_do_not_track,
                    'require_consent' => $config->require_consent,
                    'consent_settings' => $config->consent_settings ?? [],
                    'debug_mode' => $config->debug_mode,
                    'automatic_matching' => $config->automatic_matching,
                    'enable_deduplication' => $config->enable_deduplication,
                    'deduplication_method' => $config->deduplication_method,
                    'lazy_load' => $config->lazy_load,
                    'event_batch_size' => $config->event_batch_size,
                    'event_delay_ms' => $config->event_delay_ms,
                ],
                'status' => $status,
                'conversions_api_status' => $conversionsApiStatus,
                'standard_events' => $standardEvents,
                'default_consent_settings' => $this->metaPixelService->getDefaultConsentSettings(),
            ]);
        } catch (\Exception $e) {
            Log::error('Meta Pixel configuration page error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return Inertia::render('admin/MetaPixel/Index', [
                'config' => [
                    'enabled' => false,
                    'pixel_id' => '',
                    'conversions_api_enabled' => false,
                    'enabled_events' => [],
                    'debug_mode' => false,
                ],
                'status' => ['enabled' => false],
                'conversions_api_status' => ['configured' => false],
                'standard_events' => [],
                'default_consent_settings' => [],
                'error' => 'Failed to load Meta Pixel configuration',
            ]);
        }
    }

    /**
     * Update Meta Pixel configuration.
     */
    public function updateConfig(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'enabled' => 'required|boolean',
            'pixel_id' => 'required_if:enabled,true|nullable|string|regex:/^\d+$/',
            'access_token' => 'nullable|string',
            'conversions_api_enabled' => 'boolean',
            'test_event_code' => 'nullable|string',
            'enabled_events' => 'nullable|array',
            'enabled_events.*' => 'string',
            'custom_events' => 'nullable|array',
            'respect_do_not_track' => 'boolean',
            'require_consent' => 'boolean',
            'consent_settings' => 'nullable|array',
            'debug_mode' => 'boolean',
            'automatic_matching' => 'boolean',
            'enable_deduplication' => 'boolean',
            'deduplication_method' => 'nullable|string|in:event_id,fbp_fbc,custom',
            'lazy_load' => 'boolean',
            'event_batch_size' => 'nullable|integer|min:1|max:1000',
            'event_delay_ms' => 'nullable|integer|min:0|max:10000',
        ]);

        try {
            // Don't update access token if it's empty (keep existing)
            if (empty($validated['access_token'])) {
                unset($validated['access_token']);
            }

            $this->metaPixelService->updateConfig($validated);

            return redirect()->route('admin.meta-pixel.index')
                ->with('success', 'Meta Pixel configuration updated successfully.');
        } catch (\Exception $e) {
            Log::error('Meta Pixel configuration update failed', [
                'error' => $e->getMessage(),
                'data' => $validated,
            ]);

            return redirect()->route('admin.meta-pixel.index')
                ->with('error', 'Failed to update Meta Pixel configuration: ' . $e->getMessage());
        }
    }

    /**
     * Test Meta Pixel configuration.
     */
    public function testConfiguration(): RedirectResponse
    {
        try {
            $results = $this->metaPixelService->testConfiguration();

            if ($results['pixel_id_valid'] && $results['event_generation']) {
                $message = 'Meta Pixel configuration test passed successfully.';
                
                if (isset($results['conversions_api']) && $results['conversions_api']['success']) {
                    $message .= ' Conversions API connection is also working.';
                }

                return redirect()->route('admin.meta-pixel.index')
                    ->with('success', $message);
            } else {
                $errors = [];
                
                if (!$results['pixel_id_valid']) {
                    $errors[] = 'Invalid Pixel ID format';
                }
                
                if (!$results['event_generation']) {
                    $errors[] = 'Event generation failed';
                    if (isset($results['event_generation_error'])) {
                        $errors[] = $results['event_generation_error'];
                    }
                }

                if (isset($results['conversions_api']) && !$results['conversions_api']['success']) {
                    $errors[] = 'Conversions API test failed: ' . ($results['conversions_api']['error'] ?? 'Unknown error');
                }

                return redirect()->route('admin.meta-pixel.index')
                    ->with('error', 'Configuration test failed: ' . implode(', ', $errors));
            }
        } catch (\Exception $e) {
            Log::error('Meta Pixel configuration test failed', [
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('admin.meta-pixel.index')
                ->with('error', 'Configuration test failed: ' . $e->getMessage());
        }
    }

    /**
     * Test Conversions API connection.
     */
    public function testConversionsApi(): RedirectResponse
    {
        try {
            $result = $this->conversionsApiService->testConnection();

            if ($result['success']) {
                return redirect()->route('admin.meta-pixel.index')
                    ->with('success', 'Conversions API connection test successful.');
            } else {
                return redirect()->route('admin.meta-pixel.index')
                    ->with('error', 'Conversions API test failed: ' . $result['error']);
            }
        } catch (\Exception $e) {
            Log::error('Conversions API test failed', [
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('admin.meta-pixel.index')
                ->with('error', 'Conversions API test failed: ' . $e->getMessage());
        }
    }

    /**
     * Clear Meta Pixel cache.
     */
    public function clearCache(): RedirectResponse
    {
        try {
            $this->metaPixelService->clearCache();

            return redirect()->route('admin.meta-pixel.index')
                ->with('success', 'Meta Pixel cache cleared successfully.');
        } catch (\Exception $e) {
            Log::error('Meta Pixel cache clear failed', [
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('admin.meta-pixel.index')
                ->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Send a test event.
     */
    public function sendTestEvent(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'event_name' => 'required|string',
            'event_data' => 'nullable|array',
        ]);

        try {
            $result = $this->metaPixelService->trackEvent(
                $validated['event_name'],
                $validated['event_data'] ?? [],
                auth()->user()
            );

            if ($result['success']) {
                // Also send to Conversions API if enabled
                if ($this->metaPixelService->isConversionsApiEnabled()) {
                    $serverEvent = $this->conversionsApiService->createEvent(
                        $validated['event_name'],
                        $validated['event_data'] ?? [],
                        auth()->user(),
                        $result['event_id']
                    );
                    
                    $apiResult = $this->conversionsApiService->sendEvent($serverEvent);
                    
                    if ($apiResult['success']) {
                        return redirect()->route('admin.meta-pixel.index')
                            ->with('success', 'Test event sent successfully to both Pixel and Conversions API.');
                    } else {
                        return redirect()->route('admin.meta-pixel.index')
                            ->with('warning', 'Test event sent to Pixel but failed for Conversions API: ' . $apiResult['error']);
                    }
                } else {
                    return redirect()->route('admin.meta-pixel.index')
                        ->with('success', 'Test event sent successfully to Meta Pixel.');
                }
            } else {
                return redirect()->route('admin.meta-pixel.index')
                    ->with('error', 'Test event failed: ' . ($result['reason'] ?? 'Unknown error'));
            }
        } catch (\Exception $e) {
            Log::error('Meta Pixel test event failed', [
                'error' => $e->getMessage(),
                'event_name' => $validated['event_name'],
            ]);

            return redirect()->route('admin.meta-pixel.index')
                ->with('error', 'Test event failed: ' . $e->getMessage());
        }
    }
}
