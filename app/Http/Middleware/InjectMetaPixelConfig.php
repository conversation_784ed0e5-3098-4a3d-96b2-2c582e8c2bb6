<?php

namespace App\Http\Middleware;

use App\Services\MetaPixelService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;

class InjectMetaPixelConfig
{
    protected MetaPixelService $metaPixelService;

    public function __construct(MetaPixelService $metaPixelService)
    {
        $this->metaPixelService = $metaPixelService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Only inject for Inertia requests
        if ($request->header('X-Inertia')) {
            $this->injectMetaPixelConfig();
        }

        return $next($request);
    }

    /**
     * Inject Meta Pixel configuration into Inertia shared data.
     */
    protected function injectMetaPixelConfig(): void
    {
        $config = Cache::remember('meta_pixel_frontend_config', 3600, function () {
            return $this->metaPixelService->getFrontendConfig();
        });

        Inertia::share('metaPixel', $config);
    }
}
