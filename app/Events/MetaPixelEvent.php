<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MetaPixelEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $eventName;
    public array $eventData;
    public ?User $user;
    public ?string $eventId;
    public bool $sendToConversionsApi;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $eventName,
        array $eventData = [],
        ?User $user = null,
        ?string $eventId = null,
        bool $sendToConversionsApi = true
    ) {
        $this->eventName = $eventName;
        $this->eventData = $eventData;
        $this->user = $user;
        $this->eventId = $eventId;
        $this->sendToConversionsApi = $sendToConversionsApi;
    }
}
