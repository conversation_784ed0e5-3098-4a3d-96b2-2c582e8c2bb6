<?php

namespace App\Examples;

use App\Http\Controllers\Controller;
use App\Traits\TracksMetaPixelEvents;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * Example controller showing how to implement Meta Pixel tracking
 * in various scenarios. This is for demonstration purposes.
 * 
 * Adapt these patterns to your actual controllers and business logic.
 */
class MetaPixelControllerExample extends Controller
{
    use TracksMetaPixelEvents;

    /**
     * Example: Track user registration
     */
    public function register(Request $request): JsonResponse
    {
        // Validate request
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        // Create user
        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
        ]);

        // Track registration event
        $this->trackCompleteRegistration(
            contentName: 'User Registration',
            user: $user
        );

        return response()->json([
            'message' => 'User registered successfully',
            'user' => $user
        ]);
    }

    /**
     * Example: Track product purchase
     */
    public function completePurchase(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'order_id' => 'required|string',
            'total' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'items' => 'required|array',
            'items.*.product_id' => 'required|string',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric|min:0',
        ]);

        // Process the purchase (your business logic here)
        $orderId = $validated['order_id'];
        $total = $validated['total'];
        $currency = $validated['currency'];
        $items = $validated['items'];

        // Extract product IDs for tracking
        $contentIds = array_column($items, 'product_id');

        // Track purchase event
        $this->trackPurchase(
            value: $total,
            currency: $currency,
            contentIds: $contentIds,
            user: auth()->user()
        );

        return response()->json([
            'message' => 'Purchase completed successfully',
            'order_id' => $orderId
        ]);
    }

    /**
     * Example: Track lead generation
     */
    public function submitContactForm(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string|max:1000',
            'interest' => 'nullable|string|in:products,services,support',
        ]);

        // Process the contact form (your business logic here)
        // e.g., save to database, send email, etc.

        // Track lead event
        $this->trackLead(
            user: auth()->user()
        );

        // Also track as a contact event
        $this->trackContact(
            user: auth()->user()
        );

        return response()->json([
            'message' => 'Contact form submitted successfully'
        ]);
    }

    /**
     * Example: Track product view
     */
    public function viewProduct(Request $request, string $productId): JsonResponse
    {
        // Get product details (your business logic here)
        $product = [
            'id' => $productId,
            'name' => 'Example Product',
            'category' => 'Electronics',
            'price' => 99.99,
            'currency' => 'USD'
        ];

        // Track view content event
        $this->trackViewContent(
            contentType: 'product',
            contentIds: [$productId],
            contentName: $product['name'],
            value: $product['price'],
            currency: $product['currency'],
            user: auth()->user()
        );

        return response()->json([
            'product' => $product
        ]);
    }

    /**
     * Example: Track add to cart
     */
    public function addToCart(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'product_id' => 'required|string',
            'quantity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
        ]);

        // Add to cart logic (your business logic here)
        $productId = $validated['product_id'];
        $quantity = $validated['quantity'];
        $price = $validated['price'];
        $currency = $validated['currency'];

        $totalValue = $price * $quantity;

        // Track add to cart event
        $this->trackAddToCart(
            contentIds: [$productId],
            value: $totalValue,
            currency: $currency,
            user: auth()->user()
        );

        return response()->json([
            'message' => 'Product added to cart',
            'product_id' => $productId,
            'quantity' => $quantity,
            'total_value' => $totalValue
        ]);
    }

    /**
     * Example: Track search
     */
    public function search(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'query' => 'required|string|max:255',
            'category' => 'nullable|string|max:100',
        ]);

        $query = $validated['query'];
        $category = $validated['category'] ?? null;

        // Perform search (your business logic here)
        $results = [
            // Your search results
        ];

        // Track search event
        $this->trackSearch(
            searchString: $query,
            contentCategory: $category,
            user: auth()->user()
        );

        return response()->json([
            'query' => $query,
            'category' => $category,
            'results' => $results,
            'count' => count($results)
        ]);
    }

    /**
     * Example: Track newsletter subscription
     */
    public function subscribe(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'email' => 'required|email|max:255',
            'name' => 'nullable|string|max:255',
        ]);

        // Process subscription (your business logic here)
        $email = $validated['email'];
        $name = $validated['name'] ?? null;

        // Track subscribe event
        $this->trackSubscribe(
            user: auth()->user()
        );

        return response()->json([
            'message' => 'Successfully subscribed to newsletter',
            'email' => $email
        ]);
    }

    /**
     * Example: Track checkout initiation
     */
    public function initiateCheckout(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'cart_total' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'item_count' => 'required|integer|min:1',
        ]);

        $cartTotal = $validated['cart_total'];
        $currency = $validated['currency'];
        $itemCount = $validated['item_count'];

        // Initiate checkout process (your business logic here)

        // Track initiate checkout event
        $this->trackInitiateCheckout(
            value: $cartTotal,
            currency: $currency,
            numItems: $itemCount,
            user: auth()->user()
        );

        return response()->json([
            'message' => 'Checkout initiated',
            'cart_total' => $cartTotal,
            'currency' => $currency,
            'item_count' => $itemCount
        ]);
    }

    /**
     * Example: Track custom event with custom data
     */
    public function trackCustomEvent(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'event_name' => 'required|string|max:100',
            'event_data' => 'nullable|array',
        ]);

        $eventName = $validated['event_name'];
        $eventData = $validated['event_data'] ?? [];

        // Track custom event
        $this->trackMetaPixelEvent(
            eventName: $eventName,
            eventData: $eventData,
            user: auth()->user()
        );

        return response()->json([
            'message' => 'Custom event tracked',
            'event_name' => $eventName,
            'event_data' => $eventData
        ]);
    }
}
