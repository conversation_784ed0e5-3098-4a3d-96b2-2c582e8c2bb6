<?php

namespace App\Traits;

use App\Events\MetaPixelEvent;
use App\Models\User;
use Illuminate\Support\Str;

trait TracksMetaPixelEvents
{
    /**
     * Track a Meta Pixel event.
     */
    protected function trackMetaPixelEvent(
        string $eventName,
        array $eventData = [],
        ?User $user = null,
        ?string $eventId = null,
        bool $sendToConversionsApi = true
    ): void {
        // Generate event ID if not provided
        if (!$eventId) {
            $eventId = $this->generateEventId();
        }

        // Use authenticated user if not provided
        if (!$user && auth()->check()) {
            $user = auth()->user();
        }

        // Dispatch the event
        MetaPixelEvent::dispatch(
            $eventName,
            $eventData,
            $user,
            $eventId,
            $sendToConversionsApi
        );
    }

    /**
     * Track a purchase event.
     */
    protected function trackPurchase(
        float $value,
        string $currency = 'USD',
        array $contentIds = [],
        ?User $user = null,
        ?string $eventId = null
    ): void {
        $eventData = [
            'value' => $value,
            'currency' => $currency,
        ];

        if (!empty($contentIds)) {
            $eventData['content_ids'] = $contentIds;
            $eventData['content_type'] = 'product';
        }

        $this->trackMetaPixelEvent('Purchase', $eventData, $user, $eventId);
    }

    /**
     * Track a lead event.
     */
    protected function trackLead(
        ?float $value = null,
        string $currency = 'USD',
        ?User $user = null,
        ?string $eventId = null
    ): void {
        $eventData = [];

        if ($value !== null) {
            $eventData['value'] = $value;
            $eventData['currency'] = $currency;
        }

        $this->trackMetaPixelEvent('Lead', $eventData, $user, $eventId);
    }

    /**
     * Track a complete registration event.
     */
    protected function trackCompleteRegistration(
        ?string $contentName = null,
        ?float $value = null,
        string $currency = 'USD',
        ?User $user = null,
        ?string $eventId = null
    ): void {
        $eventData = [];

        if ($contentName) {
            $eventData['content_name'] = $contentName;
        }

        if ($value !== null) {
            $eventData['value'] = $value;
            $eventData['currency'] = $currency;
        }

        $this->trackMetaPixelEvent('CompleteRegistration', $eventData, $user, $eventId);
    }

    /**
     * Track a view content event.
     */
    protected function trackViewContent(
        string $contentType,
        array $contentIds = [],
        ?string $contentName = null,
        ?float $value = null,
        string $currency = 'USD',
        ?User $user = null,
        ?string $eventId = null
    ): void {
        $eventData = [
            'content_type' => $contentType,
        ];

        if (!empty($contentIds)) {
            $eventData['content_ids'] = $contentIds;
        }

        if ($contentName) {
            $eventData['content_name'] = $contentName;
        }

        if ($value !== null) {
            $eventData['value'] = $value;
            $eventData['currency'] = $currency;
        }

        $this->trackMetaPixelEvent('ViewContent', $eventData, $user, $eventId);
    }

    /**
     * Track an add to cart event.
     */
    protected function trackAddToCart(
        array $contentIds,
        ?float $value = null,
        string $currency = 'USD',
        ?User $user = null,
        ?string $eventId = null
    ): void {
        $eventData = [
            'content_ids' => $contentIds,
            'content_type' => 'product',
        ];

        if ($value !== null) {
            $eventData['value'] = $value;
            $eventData['currency'] = $currency;
        }

        $this->trackMetaPixelEvent('AddToCart', $eventData, $user, $eventId);
    }

    /**
     * Track an initiate checkout event.
     */
    protected function trackInitiateCheckout(
        ?float $value = null,
        string $currency = 'USD',
        ?int $numItems = null,
        ?User $user = null,
        ?string $eventId = null
    ): void {
        $eventData = [];

        if ($value !== null) {
            $eventData['value'] = $value;
            $eventData['currency'] = $currency;
        }

        if ($numItems !== null) {
            $eventData['num_items'] = $numItems;
        }

        $this->trackMetaPixelEvent('InitiateCheckout', $eventData, $user, $eventId);
    }

    /**
     * Track a search event.
     */
    protected function trackSearch(
        string $searchString,
        ?string $contentCategory = null,
        ?User $user = null,
        ?string $eventId = null
    ): void {
        $eventData = [
            'search_string' => $searchString,
        ];

        if ($contentCategory) {
            $eventData['content_category'] = $contentCategory;
        }

        $this->trackMetaPixelEvent('Search', $eventData, $user, $eventId);
    }

    /**
     * Track a contact event.
     */
    protected function trackContact(
        ?User $user = null,
        ?string $eventId = null
    ): void {
        $this->trackMetaPixelEvent('Contact', [], $user, $eventId);
    }

    /**
     * Track a subscribe event.
     */
    protected function trackSubscribe(
        ?float $value = null,
        string $currency = 'USD',
        ?User $user = null,
        ?string $eventId = null
    ): void {
        $eventData = [];

        if ($value !== null) {
            $eventData['value'] = $value;
            $eventData['currency'] = $currency;
        }

        $this->trackMetaPixelEvent('Subscribe', $eventData, $user, $eventId);
    }

    /**
     * Generate a unique event ID for deduplication.
     */
    protected function generateEventId(): string
    {
        return time() . '-' . Str::random(9);
    }
}
