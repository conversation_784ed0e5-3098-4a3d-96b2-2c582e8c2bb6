<?php

namespace App\Services;

use App\Models\MetaPixelConfig;
use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ConversionsApiService
{
    protected MetaPixelConfig $config;
    protected string $endpoint;

    public function __construct()
    {
        $this->config = MetaPixelConfig::current();
        $this->endpoint = config('meta-pixel.conversions_api.endpoint', 'https://graph.facebook.com/v21.0');
    }

    /**
     * Send a single event to the Conversions API.
     */
    public function sendEvent(array $eventData): array
    {
        return $this->sendEvents([$eventData]);
    }

    /**
     * Send multiple events to the Conversions API.
     */
    public function sendEvents(array $events): array
    {
        if (!$this->config->isConversionsApiConfigured()) {
            return [
                'success' => false,
                'error' => 'Conversions API not properly configured',
            ];
        }

        if (empty($events)) {
            return [
                'success' => false,
                'error' => 'No events to send',
            ];
        }

        try {
            $url = "{$this->endpoint}/{$this->config->pixel_id}/events";
            
            $payload = [
                'data' => $events,
                'access_token' => $this->config->access_token,
            ];

            // Add test event code if configured
            if (!empty($this->config->test_event_code)) {
                $payload['test_event_code'] = $this->config->test_event_code;
            }

            $response = Http::timeout(config('meta-pixel.conversions_api.timeout', 30))
                ->post($url, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                
                Log::info('Conversions API events sent successfully', [
                    'events_count' => count($events),
                    'events_received' => $responseData['events_received'] ?? 0,
                    'messages' => $responseData['messages'] ?? [],
                ]);

                return [
                    'success' => true,
                    'events_received' => $responseData['events_received'] ?? 0,
                    'messages' => $responseData['messages'] ?? [],
                    'fbtrace_id' => $responseData['fbtrace_id'] ?? null,
                ];
            } else {
                $error = $response->json();
                
                Log::error('Conversions API request failed', [
                    'status' => $response->status(),
                    'error' => $error,
                    'events_count' => count($events),
                ]);

                return [
                    'success' => false,
                    'error' => $error['error']['message'] ?? 'Unknown error',
                    'error_code' => $error['error']['code'] ?? null,
                    'status_code' => $response->status(),
                ];
            }
        } catch (\Exception $e) {
            Log::error('Conversions API exception', [
                'message' => $e->getMessage(),
                'events_count' => count($events),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create a server-side event for the Conversions API.
     */
    public function createEvent(
        string $eventName,
        array $customData = [],
        ?User $user = null,
        ?string $eventId = null,
        ?int $eventTime = null
    ): array {
        $eventId = $eventId ?? (string) Str::uuid();
        $eventTime = $eventTime ?? time();

        $event = [
            'event_name' => $eventName,
            'event_time' => $eventTime,
            'event_id' => $eventId,
            'action_source' => 'website',
            'event_source_url' => request()->fullUrl(),
        ];

        // Add user data
        $userData = $this->getUserDataForApi($user);
        if (!empty($userData)) {
            $event['user_data'] = $userData;
        }

        // Add custom data
        if (!empty($customData)) {
            $event['custom_data'] = $this->sanitizeCustomData($customData);
        }

        return $event;
    }

    /**
     * Get user data formatted for Conversions API.
     */
    protected function getUserDataForApi(?User $user = null): array
    {
        $userData = [];

        if ($user) {
            // Email (hashed)
            if ($user->email) {
                $userData['em'] = hash('sha256', strtolower(trim($user->email)));
            }

            // Phone (hashed)
            if ($user->phone) {
                $phone = preg_replace('/[^0-9]/', '', $user->phone);
                $userData['ph'] = hash('sha256', $phone);
            }

            // First name (hashed)
            if ($user->first_name) {
                $userData['fn'] = hash('sha256', strtolower(trim($user->first_name)));
            }

            // Last name (hashed)
            if ($user->last_name) {
                $userData['ln'] = hash('sha256', strtolower(trim($user->last_name)));
            }

            // External ID
            $userData['external_id'] = (string) $user->id;
        }

        // Client information
        $request = request();
        if ($request) {
            $userData['client_ip_address'] = $request->ip();
            $userData['client_user_agent'] = $request->userAgent();

            // Facebook browser cookies for deduplication
            if ($this->config->enable_deduplication) {
                $fbp = $request->cookie('_fbp');
                $fbc = $request->cookie('_fbc');

                if ($fbp) {
                    $userData['fbp'] = $fbp;
                }

                if ($fbc) {
                    $userData['fbc'] = $fbc;
                }
            }
        }

        return $userData;
    }

    /**
     * Sanitize custom data for API submission.
     */
    protected function sanitizeCustomData(array $customData): array
    {
        $sanitized = [];

        foreach ($customData as $key => $value) {
            // Convert value to appropriate type
            if (is_numeric($value)) {
                $sanitized[$key] = (float) $value;
            } elseif (is_string($value)) {
                $sanitized[$key] = trim($value);
            } elseif (is_array($value)) {
                $sanitized[$key] = array_values($value); // Ensure indexed array
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Track a PageView event.
     */
    public function trackPageView(?User $user = null, ?string $eventId = null): array
    {
        $event = $this->createEvent('PageView', [], $user, $eventId);
        return $this->sendEvent($event);
    }

    /**
     * Track a Purchase event.
     */
    public function trackPurchase(
        float $value,
        string $currency,
        array $contentIds = [],
        ?User $user = null,
        ?string $eventId = null
    ): array {
        $customData = [
            'value' => $value,
            'currency' => $currency,
        ];

        if (!empty($contentIds)) {
            $customData['content_ids'] = $contentIds;
            $customData['content_type'] = 'product';
        }

        $event = $this->createEvent('Purchase', $customData, $user, $eventId);
        return $this->sendEvent($event);
    }

    /**
     * Track a Lead event.
     */
    public function trackLead(
        ?float $value = null,
        ?string $currency = null,
        ?User $user = null,
        ?string $eventId = null
    ): array {
        $customData = [];

        if ($value !== null) {
            $customData['value'] = $value;
        }

        if ($currency !== null) {
            $customData['currency'] = $currency;
        }

        $event = $this->createEvent('Lead', $customData, $user, $eventId);
        return $this->sendEvent($event);
    }

    /**
     * Track a Search event.
     */
    public function trackSearch(
        string $searchString,
        ?string $contentCategory = null,
        ?User $user = null,
        ?string $eventId = null
    ): array {
        $customData = [
            'search_string' => $searchString,
        ];

        if ($contentCategory) {
            $customData['content_category'] = $contentCategory;
        }

        $event = $this->createEvent('Search', $customData, $user, $eventId);
        return $this->sendEvent($event);
    }

    /**
     * Track a ViewContent event.
     */
    public function trackViewContent(
        string $contentType,
        array $contentIds = [],
        ?string $contentName = null,
        ?User $user = null,
        ?string $eventId = null
    ): array {
        $customData = [
            'content_type' => $contentType,
        ];

        if (!empty($contentIds)) {
            $customData['content_ids'] = $contentIds;
        }

        if ($contentName) {
            $customData['content_name'] = $contentName;
        }

        $event = $this->createEvent('ViewContent', $customData, $user, $eventId);
        return $this->sendEvent($event);
    }

    /**
     * Test the Conversions API connection.
     */
    public function testConnection(): array
    {
        if (!$this->config->isConversionsApiConfigured()) {
            return [
                'success' => false,
                'error' => 'Conversions API not configured',
            ];
        }

        try {
            // Send a test PageView event
            $testEvent = $this->createEvent('PageView', [], null, 'test-' . Str::uuid());
            $result = $this->sendEvent($testEvent);

            return [
                'success' => $result['success'],
                'message' => $result['success'] ? 'Connection successful' : $result['error'],
                'details' => $result,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get Conversions API status.
     */
    public function getStatus(): array
    {
        return [
            'configured' => $this->config->isConversionsApiConfigured(),
            'pixel_id' => $this->config->pixel_id,
            'access_token_configured' => !empty($this->config->access_token),
            'test_event_code' => $this->config->test_event_code,
            'endpoint' => $this->endpoint,
        ];
    }
}
