<?php

namespace App\Services;

use App\Models\MetaPixelConfig;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class MetaPixelService
{
    protected MetaPixelConfig $config;

    public function __construct()
    {
        $this->config = MetaPixelConfig::current();
    }

    /**
     * Get the current Meta Pixel configuration.
     */
    public function getConfig(): MetaPixelConfig
    {
        return $this->config;
    }

    /**
     * Update Meta Pixel configuration.
     */
    public function updateConfig(array $data): MetaPixelConfig
    {
        $this->config->update($data);
        $this->config->refresh();
        
        Log::info('Meta Pixel configuration updated', [
            'enabled' => $this->config->enabled,
            'conversions_api_enabled' => $this->config->conversions_api_enabled,
        ]);

        return $this->config;
    }

    /**
     * Check if Meta Pixel is enabled and properly configured.
     */
    public function isEnabled(): bool
    {
        return $this->config->enabled && $this->config->isConfigured();
    }

    /**
     * Check if Conversions API is enabled and properly configured.
     */
    public function isConversionsApiEnabled(): bool
    {
        return $this->config->conversions_api_enabled && $this->config->isConversionsApiConfigured();
    }

    /**
     * Get configuration for frontend JavaScript.
     */
    public function getFrontendConfig(): array
    {
        if (!$this->isEnabled()) {
            return ['enabled' => false];
        }

        return $this->config->getFrontendConfig();
    }

    /**
     * Generate a unique event ID for deduplication.
     */
    public function generateEventId(): string
    {
        return (string) Str::uuid();
    }

    /**
     * Check if an event should be tracked based on configuration.
     */
    public function shouldTrackEvent(string $eventName): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        return $this->config->isEventEnabled($eventName);
    }

    /**
     * Get user data for Meta Pixel tracking.
     */
    public function getUserData(?User $user = null): array
    {
        $userData = [];

        if ($user) {
            // Hash sensitive data for privacy
            if ($user->email) {
                $userData['em'] = hash('sha256', strtolower(trim($user->email)));
            }

            if ($user->phone) {
                // Remove non-numeric characters and hash
                $phone = preg_replace('/[^0-9]/', '', $user->phone);
                $userData['ph'] = hash('sha256', $phone);
            }

            if ($user->first_name) {
                $userData['fn'] = hash('sha256', strtolower(trim($user->first_name)));
            }

            if ($user->last_name) {
                $userData['ln'] = hash('sha256', strtolower(trim($user->last_name)));
            }

            // Add user ID for internal tracking
            $userData['external_id'] = (string) $user->id;
        }

        // Add client information
        $request = request();
        if ($request) {
            $userData['client_ip_address'] = $request->ip();
            $userData['client_user_agent'] = $request->userAgent();
        }

        return $userData;
    }

    /**
     * Get standard event parameters for a specific event type.
     */
    public function getStandardEventParameters(string $eventName): array
    {
        $standardEvents = config('meta-pixel.standard_events', []);
        
        return $standardEvents[$eventName]['parameters'] ?? [];
    }

    /**
     * Validate event data against standard event requirements.
     */
    public function validateEventData(string $eventName, array $eventData): array
    {
        $errors = [];
        $standardParams = $this->getStandardEventParameters($eventName);

        // Check for required parameters based on event type
        switch ($eventName) {
            case 'Purchase':
                if (!isset($eventData['value']) || !is_numeric($eventData['value'])) {
                    $errors[] = 'Purchase events require a numeric value parameter';
                }
                if (!isset($eventData['currency'])) {
                    $errors[] = 'Purchase events require a currency parameter';
                }
                break;

            case 'ViewContent':
                if (!isset($eventData['content_type'])) {
                    $errors[] = 'ViewContent events require a content_type parameter';
                }
                break;

            case 'Search':
                if (!isset($eventData['search_string'])) {
                    $errors[] = 'Search events require a search_string parameter';
                }
                break;

            case 'AddToCart':
                if (!isset($eventData['content_ids']) || !is_array($eventData['content_ids'])) {
                    $errors[] = 'AddToCart events require content_ids as an array';
                }
                break;
        }

        return $errors;
    }

    /**
     * Track a custom event (for frontend use).
     */
    public function trackEvent(string $eventName, array $eventData = [], ?User $user = null): array
    {
        if (!$this->shouldTrackEvent($eventName)) {
            return ['success' => false, 'reason' => 'Event tracking disabled'];
        }

        // Validate event data
        $validationErrors = $this->validateEventData($eventName, $eventData);
        if (!empty($validationErrors)) {
            return ['success' => false, 'errors' => $validationErrors];
        }

        $eventId = $this->generateEventId();
        $userData = $this->getUserData($user);

        // Prepare event for frontend tracking
        $event = [
            'event_name' => $eventName,
            'event_id' => $eventId,
            'event_time' => time(),
            'user_data' => $userData,
            'custom_data' => $eventData,
            'event_source_url' => request()->fullUrl(),
            'action_source' => 'website',
        ];

        // Log event for debugging
        if ($this->config->debug_mode) {
            Log::info('Meta Pixel event tracked', [
                'event_name' => $eventName,
                'event_id' => $eventId,
                'event_data' => $eventData,
            ]);
        }

        return [
            'success' => true,
            'event' => $event,
            'event_id' => $eventId,
        ];
    }

    /**
     * Get Meta Pixel status and health information.
     */
    public function getStatus(): array
    {
        return [
            'enabled' => $this->isEnabled(),
            'conversions_api_enabled' => $this->isConversionsApiEnabled(),
            'pixel_id' => $this->config->pixel_id,
            'pixel_id_valid' => !empty($this->config->pixel_id) && preg_match('/^\d+$/', $this->config->pixel_id),
            'access_token_configured' => !empty($this->config->access_token),
            'debug_mode' => $this->config->debug_mode,
            'enabled_events_count' => count($this->config->enabled_events ?? []),
            'deduplication_enabled' => $this->config->enable_deduplication,
            'consent_required' => $this->config->require_consent,
        ];
    }

    /**
     * Test Meta Pixel configuration.
     */
    public function testConfiguration(): array
    {
        $results = [];

        // Test basic configuration
        $results['pixel_id_valid'] = !empty($this->config->pixel_id) && preg_match('/^\d+$/', $this->config->pixel_id);
        $results['access_token_configured'] = !empty($this->config->access_token);

        // Test if we can generate events
        try {
            $testEvent = $this->trackEvent('PageView', [], null);
            $results['event_generation'] = $testEvent['success'];
        } catch (\Exception $e) {
            $results['event_generation'] = false;
            $results['event_generation_error'] = $e->getMessage();
        }

        // Test Conversions API if enabled
        if ($this->isConversionsApiEnabled()) {
            $conversionsApiService = app(ConversionsApiService::class);
            $results['conversions_api'] = $conversionsApiService->testConnection();
        }

        return $results;
    }

    /**
     * Clear all Meta Pixel related caches.
     */
    public function clearCache(): void
    {
        MetaPixelConfig::clearCache();
        Cache::forget('meta_pixel_frontend_config');
        
        Log::info('Meta Pixel cache cleared');
    }

    /**
     * Get available standard events.
     */
    public function getStandardEvents(): array
    {
        return config('meta-pixel.standard_events', []);
    }

    /**
     * Get default consent settings.
     */
    public function getDefaultConsentSettings(): array
    {
        return config('meta-pixel.default_consent_settings', []);
    }
}
