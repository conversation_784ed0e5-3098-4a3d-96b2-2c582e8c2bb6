<?php

namespace App\Console\Commands;

use App\Services\ConversionsApiService;
use App\Services\MetaPixelService;
use Illuminate\Console\Command;

class TestMetaPixelCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meta-pixel:test 
                            {--event=PageView : The event name to test}
                            {--skip-conversions-api : Skip testing Conversions API}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Meta Pixel configuration and Conversions API connectivity';

    protected MetaPixelService $metaPixelService;
    protected ConversionsApiService $conversionsApiService;

    /**
     * Create a new command instance.
     */
    public function __construct(
        MetaPixelService $metaPixelService,
        ConversionsApiService $conversionsApiService
    ) {
        parent::__construct();
        $this->metaPixelService = $metaPixelService;
        $this->conversionsApiService = $conversionsApiService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Testing Meta Pixel Configuration...');
        $this->newLine();

        // Test basic configuration
        $this->testBasicConfiguration();

        // Test Conversions API if not skipped
        if (!$this->option('skip-conversions-api')) {
            $this->testConversionsApi();
        }

        $this->newLine();
        $this->info('Meta Pixel test completed.');

        return Command::SUCCESS;
    }

    /**
     * Test basic Meta Pixel configuration.
     */
    protected function testBasicConfiguration(): void
    {
        $this->info('🔍 Testing Basic Configuration');

        // Check if Meta Pixel is enabled
        if (!$this->metaPixelService->isEnabled()) {
            $this->error('❌ Meta Pixel is disabled');
            return;
        }

        $this->info('✅ Meta Pixel is enabled');

        // Get configuration
        $config = $this->metaPixelService->getConfig();

        if (!$config) {
            $this->error('❌ No Meta Pixel configuration found');
            return;
        }

        $this->info('✅ Meta Pixel configuration found');

        // Display configuration details
        $this->table(['Setting', 'Value'], [
            ['Pixel ID', $config->pixel_id ?? 'Not set'],
            ['Debug Mode', $config->debug_mode ? 'Enabled' : 'Disabled'],
            ['Conversions API', $config->conversions_api_enabled ? 'Enabled' : 'Disabled'],
            ['Respect Do Not Track', $config->respect_do_not_track ? 'Yes' : 'No'],
            ['Require Consent', $config->require_consent ? 'Yes' : 'No'],
            ['Automatic Matching', $config->automatic_matching ? 'Enabled' : 'Disabled'],
            ['Deduplication', $config->enable_deduplication ? 'Enabled' : 'Disabled'],
        ]);

        // Test event validation
        $eventName = $this->option('event');
        if ($this->metaPixelService->shouldTrackEvent($eventName)) {
            $this->info("✅ Event '{$eventName}' is enabled for tracking");
        } else {
            $this->warn("⚠️ Event '{$eventName}' is not enabled for tracking");
        }
    }

    /**
     * Test Conversions API connectivity.
     */
    protected function testConversionsApi(): void
    {
        $this->newLine();
        $this->info('🔍 Testing Conversions API');

        if (!$this->metaPixelService->isConversionsApiEnabled()) {
            $this->warn('⚠️ Conversions API is disabled');
            return;
        }

        $this->info('✅ Conversions API is enabled');

        // Test API connectivity
        $this->info('Testing API connectivity...');

        try {
            // Create a test event
            $testEvent = $this->conversionsApiService->createEvent(
                $this->option('event'),
                ['test' => true],
                null,
                'test-' . time()
            );

            $this->info('✅ Test event created successfully');

            // Test sending the event
            $result = $this->conversionsApiService->sendEvent($testEvent);

            if ($result['success']) {
                $this->info('✅ Test event sent successfully to Conversions API');
                $this->info("Events received: {$result['events_received']}");
                
                if (isset($result['messages'])) {
                    foreach ($result['messages'] as $message) {
                        $this->line("Message: {$message}");
                    }
                }
            } else {
                $this->error('❌ Failed to send test event to Conversions API');
                $this->error("Error: {$result['error']}");
                
                if (isset($result['details'])) {
                    $this->error("Details: " . json_encode($result['details'], JSON_PRETTY_PRINT));
                }
            }
        } catch (\Exception $e) {
            $this->error('❌ Conversions API test failed with exception');
            $this->error("Error: {$e->getMessage()}");
            
            if ($this->getOutput()->isVerbose()) {
                $this->error("Trace: {$e->getTraceAsString()}");
            }
        }
    }
}
