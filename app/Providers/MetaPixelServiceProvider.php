<?php

namespace App\Providers;

use App\Services\MetaPixelService;
use App\Services\ConversionsApiService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class MetaPixelServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register Meta Pixel Service as singleton
        $this->app->singleton(MetaPixelService::class, function ($app) {
            return new MetaPixelService();
        });

        // Register Conversions API Service as singleton
        $this->app->singleton(ConversionsApiService::class, function ($app) {
            return new ConversionsApiService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share Meta Pixel configuration with all views
        View::composer('*', function ($view) {
            if (app()->bound(MetaPixelService::class)) {
                $metaPixelService = app(MetaPixelService::class);
                $view->with('metaPixelConfig', $metaPixelService->getFrontendConfig());
            }
        });

        // Register console commands if running in console
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\TestMetaPixelCommand::class,
            ]);
        }
    }
}
