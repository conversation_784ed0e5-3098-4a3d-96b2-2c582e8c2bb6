<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use Illuminate\Mail\Events\MessageSending;
use Illuminate\Mail\Events\MessageSent;
use App\Events\EmailSent;
use App\Events\MetaPixelEvent;
use App\Listeners\LogEmailSent;
use App\Listeners\LogEmailEvents;
use App\Listeners\SendMetaPixelEvent;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        EmailSent::class => [
            LogEmailSent::class,
        ],
        MessageSending::class => [
            LogEmailEvents::class,
        ],
        MessageSent::class => [
            LogEmailEvents::class,
        ],
        MetaPixelEvent::class => [
            SendMetaPixelEvent::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        parent::boot();
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
