<?php

namespace App\Listeners;

use App\Events\MetaPixelEvent;
use App\Services\ConversionsApiService;
use App\Services\MetaPixelService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendMetaPixelEvent implements ShouldQueue
{
    use InteractsWithQueue;

    protected MetaPixelService $metaPixelService;
    protected ConversionsApiService $conversionsApiService;

    /**
     * Create the event listener.
     */
    public function __construct(
        MetaPixelService $metaPixelService,
        ConversionsApiService $conversionsApiService
    ) {
        $this->metaPixelService = $metaPixelService;
        $this->conversionsApiService = $conversionsApiService;
    }

    /**
     * Handle the event.
     */
    public function handle(MetaPixelEvent $event): void
    {
        // Check if Meta Pixel is enabled
        if (!$this->metaPixelService->isEnabled()) {
            Log::debug('Meta Pixel event skipped - service disabled', [
                'event_name' => $event->eventName,
            ]);
            return;
        }

        // Check if this event should be tracked
        if (!$this->metaPixelService->shouldTrackEvent($event->eventName)) {
            Log::debug('Meta Pixel event skipped - event not enabled', [
                'event_name' => $event->eventName,
            ]);
            return;
        }

        // Send to Conversions API if enabled and requested
        if ($event->sendToConversionsApi && $this->metaPixelService->isConversionsApiEnabled()) {
            try {
                $serverEvent = $this->conversionsApiService->createEvent(
                    $event->eventName,
                    $event->eventData,
                    $event->user,
                    $event->eventId
                );

                $result = $this->conversionsApiService->sendEvent($serverEvent);

                if ($result['success']) {
                    Log::info('Meta Pixel Conversions API event sent successfully', [
                        'event_name' => $event->eventName,
                        'event_id' => $event->eventId,
                        'events_received' => $result['events_received'] ?? 0,
                    ]);
                } else {
                    Log::error('Meta Pixel Conversions API event failed', [
                        'event_name' => $event->eventName,
                        'event_id' => $event->eventId,
                        'error' => $result['error'] ?? 'Unknown error',
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Meta Pixel Conversions API exception', [
                    'event_name' => $event->eventName,
                    'event_id' => $event->eventId,
                    'error' => $e->getMessage(),
                ]);

                // Re-throw if this is a critical error that should fail the job
                if ($e instanceof \InvalidArgumentException) {
                    throw $e;
                }
            }
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(MetaPixelEvent $event, \Throwable $exception): void
    {
        Log::error('Meta Pixel event listener failed', [
            'event_name' => $event->eventName,
            'event_id' => $event->eventId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
